(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{2601:function(e,t,s){"use strict";var n,r;e.exports=(null==(n=s.g.process)?void 0:n.env)&&"object"==typeof(null==(r=s.g.process)?void 0:r.env)?s.g.process:s(8960)},7108:function(e,t,s){Promise.resolve().then(s.bind(s,4991))},4991:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return Home}});var n=s(7437),r=s(2265),i=s(2601);let a=i.env.NEXT_PUBLIC_API_URL||"http://localhost:8000",c=new class{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s="".concat(this.baseUrl).concat(e),n=await fetch(s,{headers:{"Content-Type":"application/json",...t.headers},...t});if(!n.ok){let e=await n.json().catch(()=>({error:"Unknown error"}));throw Error("API Error ".concat(n.status,": ").concat(e.error||n.statusText))}return n.json()}async getHealth(){return this.request("/health")}async getApiInfo(){return this.request("/")}async getComplianceStats(){return this.request("/api/v1/checklist/stats")}async getChecklistItems(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.limit&&t.append("limit",e.limit.toString()),e.offset&&t.append("offset",e.offset.toString()),e.category&&t.append("category",e.category),e.phase&&t.append("phase",e.phase.toString()),e.search&&t.append("search",e.search);let s="/api/v1/checklist/items".concat(t.toString()?"?".concat(t.toString()):"");return this.request(s)}async getFEMACategories(){return this.request("/api/v1/checklist/categories")}constructor(e=a){this.baseUrl=e}};function Dashboard(){let{health:e,loading:t,error:s}=function(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0),[i,a]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async function(){try{n(!0);let e=await c.getHealth();t(e),a(null)}catch(e){a(e instanceof Error?e.message:"Unknown error"),t(null)}finally{n(!1)}})()},[]),{health:e,loading:s,error:i}}(),{stats:i,loading:a,error:l}=function(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0),[i,a]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async function(){try{n(!0);let e=await c.getComplianceStats();t(e),a(null)}catch(e){a(e instanceof Error?e.message:"Unknown error"),t(null)}finally{n(!1)}})()},[]),{stats:e,loading:s,error:i,refetch:()=>fetchStats()}}(),{items:o,loading:d,error:u}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,s]=(0,r.useState)([]),[n,i]=(0,r.useState)(!0),[a,l]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async function(){try{i(!0);let t=await c.getChecklistItems(e);s(t),l(null)}catch(e){l(e instanceof Error?e.message:"Unknown error"),s([])}finally{i(!1)}})()},[e.limit,e.offset,e.category,e.phase,e.search]),{items:t,loading:n,error:a}}({limit:5});return(0,n.jsxs)("div",{className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-blue-600 mb-2",children:"ComplianceMax V74 Dashboard"}),(0,n.jsx)("p",{className:"text-gray-600",children:"FEMA Public Assistance Compliance Management System"})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Backend Connection Status"}),t&&(0,n.jsx)("div",{className:"text-blue-600",children:"\uD83D\uDD04 Connecting to API..."}),s&&(0,n.jsxs)("div",{className:"text-red-600",children:["❌ API Offline: ",s]}),e&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"text-lg font-medium ".concat("healthy"===e.status?"text-green-600":"text-yellow-600"),children:["✅ API Status: ",e.status]}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Version: ",e.version," | Database: ",e.database]}),(0,n.jsx)("div",{className:"flex gap-2 mt-2",children:Object.entries(e.services).map(e=>{let[t,s]=e;return(0,n.jsxs)("span",{className:"px-2 py-1 rounded text-xs ".concat(s.includes("operational")||s.includes("connected")?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:[t,": ",s]},t)})})]})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Compliance Statistics"}),a&&(0,n.jsx)("div",{className:"text-blue-600",children:"\uD83D\uDCCA Loading statistics..."}),l&&(0,n.jsxs)("div",{className:"text-red-600",children:["❌ Stats Error: ",l]}),i&&(0,n.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.total_items}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Total Items"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-600",children:Object.keys(i.by_category).length}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Categories"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:Object.keys(i.by_phase).length}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Phases"})]}),(0,n.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:i.conditional_logic_items}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"IF-THEN Rules"})]})]})]}),i&&(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Category Breakdown"}),(0,n.jsx)("div",{className:"space-y-2",children:Object.entries(i.by_category).sort((e,t)=>{let[,s]=e,[,n]=t;return n-s}).slice(0,10).map(e=>{let[t,s]=e;return(0,n.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,n.jsx)("span",{className:"font-medium",children:t}),(0,n.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm",children:s})]},t)})})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Recent Compliance Items"}),d&&(0,n.jsx)("div",{className:"text-blue-600",children:"\uD83D\uDCCB Loading items..."}),u&&(0,n.jsxs)("div",{className:"text-red-600",children:["❌ Items Error: ",u]}),o&&o.length>0?(0,n.jsx)("div",{className:"space-y-4",children:o.map(e=>(0,n.jsxs)("div",{className:"p-4 border rounded-lg hover:bg-gray-50",children:[(0,n.jsx)("h3",{className:"font-semibold text-blue-600",children:e.title||"Untitled Item"}),(0,n.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:e.description||"No description available"}),(0,n.jsxs)("div",{className:"flex gap-2 mt-2",children:[e.category&&(0,n.jsx)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs",children:e.category}),e.pappg_version&&(0,n.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded text-xs",children:e.pappg_version}),e.phase&&(0,n.jsxs)("span",{className:"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs",children:["Phase ",e.phase]})]}),e.trigger_condition_if&&(0,n.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[(0,n.jsx)("strong",{children:"IF:"})," ",e.trigger_condition_if.slice(0,100),"..."]})]},e.id))}):(0,n.jsx)("div",{className:"text-gray-500",children:"No items loaded yet."})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md border",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"System Status"}),(0,n.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Frontend:"})," Next.js (Running on :3333)"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Backend:"})," FastAPI ",e?"(v".concat(e.version,")"):"(Checking...)"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Data Processing:"})," ",i?"✅ Active":"⏳ Loading"]})]})]})]})}function Home(){let[e,t]=(0,r.useState)({frontend:"checking",backend:"checking",data:"checking"});(0,r.useEffect)(()=>{t(e=>({...e,frontend:"connected"})),fetch("http://localhost:8000/health").then(e=>e.json()).then(e=>{t(e=>({...e,backend:"connected"}))}).catch(e=>{t(e=>({...e,backend:"error"}))}),fetch("http://localhost:8000/api/v1/checklist/stats").then(e=>e.json()).then(e=>{e.total_items>0?t(e=>({...e,data:"loaded"})):t(e=>({...e,data:"empty"}))}).catch(e=>{t(e=>({...e,data:"error"}))})},[]);let getStatusColor=e=>{switch(e){case"connected":case"loaded":return"text-green-600";case"checking":return"text-blue-600";case"error":case"empty":return"text-red-600";default:return"text-gray-600"}},getStatusIcon=e=>{switch(e){case"connected":case"loaded":return"✅";case"checking":return"\uD83D\uDD04";case"error":case"empty":return"❌";default:return"⚪"}};return(0,n.jsxs)("main",{children:[(0,n.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4",children:(0,n.jsxs)("div",{className:"container mx-auto",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"\uD83D\uDE80 ComplianceMax V74 - LIVE TESTING DASHBOARD"}),(0,n.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 ".concat(getStatusColor(e.frontend)),children:[getStatusIcon(e.frontend),(0,n.jsx)("strong",{children:"Frontend (Next.js):"})," ",e.frontend]}),(0,n.jsxs)("div",{className:"flex items-center gap-2 ".concat(getStatusColor(e.backend)),children:[getStatusIcon(e.backend),(0,n.jsx)("strong",{children:"Backend (FastAPI):"})," ",e.backend]}),(0,n.jsxs)("div",{className:"flex items-center gap-2 ".concat(getStatusColor(e.data)),children:[getStatusIcon(e.data),(0,n.jsx)("strong",{children:"Compliance Data:"})," ",e.data]})]})]})}),(0,n.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 m-4",children:(0,n.jsx)("div",{className:"flex",children:(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,n.jsx)("strong",{children:"\uD83E\uDDEA INTEGRATION TEST MODE:"})," This dashboard is testing real-time connections between:",(0,n.jsx)("br",{}),"• Next.js Frontend (Port 3333) ↔ FastAPI Backend (Port 8000) ↔ Processed Compliance Data (194 items)",(0,n.jsx)("br",{}),"• Path resolution fixes ✅ | Import script working ✅ | Real FEMA data loading ✅"]})})})}),(0,n.jsx)(Dashboard,{}),(0,n.jsxs)("div",{className:"m-6 p-6 bg-gray-50 rounded-lg border",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"\uD83C\uDFD7️ System Architecture Status"}),(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium mb-2",children:"✅ Working Components"}),(0,n.jsxs)("ul",{className:"text-sm space-y-1 text-green-700",children:[(0,n.jsx)("li",{children:"• Next.js frontend running (Port 3333)"}),(0,n.jsx)("li",{children:"• FastAPI backend API (Port 8000)"}),(0,n.jsx)("li",{children:"• Data import scripts (194 items processed)"}),(0,n.jsx)("li",{children:"• Path resolution across directories"}),(0,n.jsx)("li",{children:"• IF-THEN conditional logic processing"}),(0,n.jsx)("li",{children:"• PAPPG version determination"}),(0,n.jsx)("li",{children:"• TypeScript API client integration"}),(0,n.jsx)("li",{children:"• React dashboard components"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium mb-2",children:"\uD83D\uDD27 Integration Points"}),(0,n.jsxs)("ul",{className:"text-sm space-y-1 text-blue-700",children:[(0,n.jsx)("li",{children:"• DOCS folder data ↔ Import scripts"}),(0,n.jsx)("li",{children:"• Processed JSON ↔ FastAPI endpoints"}),(0,n.jsx)("li",{children:"• API responses ↔ React components"}),(0,n.jsx)("li",{children:"• FEMA compliance rules ↔ UI display"}),(0,n.jsx)("li",{children:"• Multi-directory path resolution"}),(0,n.jsx)("li",{children:"• Real-time health monitoring"}),(0,n.jsx)("li",{children:"• Error handling & fallbacks"}),(0,n.jsx)("li",{children:"• GROK AI tag integration ready"})]})]})]})]}),(0,n.jsxs)("div",{className:"m-6 p-6 bg-green-50 rounded-lg border border-green-200",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4 text-green-800",children:"\uD83D\uDD27 DEVELOPMENT ENGINEERING STATUS"}),(0,n.jsxs)("div",{className:"text-sm text-green-700 space-y-2",children:[(0,n.jsx)("p",{children:(0,n.jsx)("strong",{children:"TEST, TEST, TEST Philosophy in Action:"})}),(0,n.jsxs)("ul",{className:"list-disc ml-6 space-y-1",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Multi-tier policy integration"})," - PAPPG v1.0-v5.0, DRRA, CFR 200, NFIP frameworks"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Event date-driven policy determination"})," - Incident date determines applicable regulations"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Conditional logic mapping"})," - Enhanced IF-THEN rules for complex compliance scenarios"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Phase 1 integration complete"})," - 7 policy frameworks integrated with existing structure"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Ready for advanced processing"})," - Docling document analysis, xAI Grok integration"]})]}),(0,n.jsxs)("p",{className:"mt-3 font-medium",children:["\uD83C\uDFAF ",(0,n.jsx)("strong",{children:"Continuing Engineering Excellence:"})," Real-time system monitoring, comprehensive testing protocols, and continuous integration validation."]})]})]})]})}},8960:function(e){!function(){var t={229:function(e){var t,s,n,r=e.exports={};function defaultSetTimout(){throw Error("setTimeout has not been defined")}function defaultClearTimeout(){throw Error("clearTimeout has not been defined")}function runTimeout(e){if(t===setTimeout)return setTimeout(e,0);if((t===defaultSetTimout||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(s){try{return t.call(null,e,0)}catch(s){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){t=defaultSetTimout}try{s="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){s=defaultClearTimeout}}();var i=[],a=!1,c=-1;function cleanUpNextTick(){a&&n&&(a=!1,n.length?i=n.concat(i):c=-1,i.length&&drainQueue())}function drainQueue(){if(!a){var e=runTimeout(cleanUpNextTick);a=!0;for(var t=i.length;t;){for(n=i,i=[];++c<t;)n&&n[c].run();c=-1,t=i.length}n=null,a=!1,function(e){if(s===clearTimeout)return clearTimeout(e);if((s===defaultClearTimeout||!s)&&clearTimeout)return s=clearTimeout,clearTimeout(e);try{s(e)}catch(t){try{return s.call(null,e)}catch(t){return s.call(this,e)}}}(e)}}function Item(e,t){this.fun=e,this.array=t}function noop(){}r.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var s=1;s<arguments.length;s++)t[s-1]=arguments[s];i.push(new Item(e,t)),1!==i.length||a||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=noop,r.addListener=noop,r.once=noop,r.off=noop,r.removeListener=noop,r.removeAllListeners=noop,r.emit=noop,r.prependListener=noop,r.prependOnceListener=noop,r.listeners=function(e){return[]},r.binding=function(e){throw Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw Error("process.chdir is not supported")},r.umask=function(){return 0}}},s={};function __nccwpck_require__(e){var n=s[e];if(void 0!==n)return n.exports;var r=s[e]={exports:{}},i=!0;try{t[e](r,r.exports,__nccwpck_require__),i=!1}finally{i&&delete s[e]}return r.exports}__nccwpck_require__.ab="//";var n=__nccwpck_require__(229);e.exports=n}()},622:function(e,t,s){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=s(2265),r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,c=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,s){var n,i={},o=null,d=null;for(n in void 0!==s&&(o=""+s),void 0!==t.key&&(o=""+t.key),void 0!==t.ref&&(d=t.ref),t)a.call(t,n)&&!l.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===i[n]&&(i[n]=t[n]);return{$$typeof:r,type:e,key:o,ref:d,props:i,_owner:c.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},7437:function(e,t,s){"use strict";e.exports=s(622)}},function(e){e.O(0,[971,472,744],function(){return e(e.s=7108)}),_N_E=e.O()}]);