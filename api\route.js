"use strict";(()=>{var e={};e.id=56,e.ids=[56],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1017:e=>{e.exports=require("path")},4739:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>_,requestAsyncStorage:()=>w,routeModule:()=>y,serverHooks:()=>C,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>T});var s={};r.r(s),r.d(s,{POST:()=>POST,PUT:()=>PUT}),r(8976);var a=r(884),o=r(6132),n=r(5798);let c=require("child_process");var i=r(1017),u=r.n(i);let d=require("fs/promises");var l=r.n(d);let p=new class{async processDocument(e,t={}){let r=Date.now();try{let s=await l().stat(e),a=u().extname(e).toLowerCase(),o=this.generateProcessingScript(e,t),n=u().join(process.cwd(),"temp_process_doc.py");await l().writeFile(n,o);let c=await this.executePythonScript(n);await l().unlink(n);let i=Date.now()-r;if(c.success)return{success:!0,extractedText:c.text||"",markdown:c.markdown||"",pages:c.pages||1,confidence:c.confidence||.8,femaCategory:t.detectFEMACategory?this.detectFEMACategory(c.text||""):void 0,metadata:{fileSize:s.size,fileType:a,processingTime:i}};return{success:!1,extractedText:"",markdown:"",pages:0,confidence:0,metadata:{fileSize:s.size,fileType:a,processingTime:i},error:c.error}}catch(s){let t=Date.now()-r;return{success:!1,extractedText:"",markdown:"",pages:0,confidence:0,metadata:{fileSize:0,fileType:u().extname(e),processingTime:t},error:s instanceof Error?s.message:"Unknown error"}}}async processDocuments(e,t={}){let r=await Promise.allSettled(e.map(e=>this.processDocument(e,t)));return r.map((t,r)=>"fulfilled"===t.status?t.value:{success:!1,extractedText:"",markdown:"",pages:0,confidence:0,metadata:{fileSize:0,fileType:u().extname(e[r]),processingTime:0},error:t.reason?.message||"Processing failed"})}generateProcessingScript(e,t){return`#!/usr/bin/env python3
import json
import sys
import os
from docling.document_converter import DocumentConverter

def process_document():
    try:
        # Initialize converter
        converter = DocumentConverter()
        
        # Process document
        result = converter.convert("${e.replace(/\\/g,"\\\\")}")
        
        # Extract information
        markdown_content = result.document.export_to_markdown()
        
        # Return results as JSON
        output = {
            "success": True,
            "text": markdown_content,
            "markdown": markdown_content,
            "pages": len(result.document.pages),
            "confidence": 0.85  # Default confidence
        }
        
        print(json.dumps(output))
        
    except Exception as e:
        error_output = {
            "success": False,
            "error": str(e)
        }
        print(json.dumps(error_output))

if __name__ == "__main__":
    process_document()
`}executePythonScript(e){return new Promise((t,r)=>{let s=(0,c.spawn)("python",[e]),a="",o="";s.stdout.on("data",e=>{a+=e.toString()}),s.stderr.on("data",e=>{o+=e.toString()}),s.on("close",e=>{if(0===e)try{let e=a.trim().split("\n"),r=e[e.length-1],s=JSON.parse(r);t(s)}catch(e){t({success:!1,error:`Failed to parse output: ${e}. Output: ${a}`})}else t({success:!1,error:`Python script failed with code ${e}. Error: ${o}`})}),s.on("error",e=>{t({success:!1,error:`Failed to start Python process: ${e.message}`})})})}detectFEMACategory(e){let t=e.toLowerCase();return t.includes("debris")||t.includes("removal")?"Category A - Debris Removal":t.includes("emergency")||t.includes("protective measures")?"Category B - Emergency Protective Measures":t.includes("road")||t.includes("bridge")?"Category C - Roads and Bridges":t.includes("water")||t.includes("dam")||t.includes("levee")?"Category D - Water Control Facilities":t.includes("building")||t.includes("equipment")?"Category E - Buildings and Equipment":t.includes("utility")||t.includes("power")||t.includes("water treatment")?"Category F - Utilities":t.includes("park")||t.includes("recreation")?"Category G - Parks & Recreation":"Uncategorized"}getSupportedFileTypes(){return[".pdf",".jpg",".jpeg",".png",".gif",".tiff",".bmp",".docx",".pptx",".xlsx",".html",".md",".txt"]}isFileTypeSupported(e){let t=u().extname(e).toLowerCase();return this.getSupportedFileTypes().includes(t)}},m=require("@prisma/client"),f=global,g=f.prisma||new m.PrismaClient({log:["error"]});async function POST(e){try{let t=await e.formData(),r=t.get("file"),s="true"===t.get("detectFEMA"),a=t.get("projectId");if(!r)return n.Z.json({error:"No file provided"},{status:400});if(!p.isFileTypeSupported(r.name))return n.Z.json({error:"Unsupported file type",supportedTypes:p.getSupportedFileTypes()},{status:400});let o=await r.arrayBuffer(),c=Buffer.from(o),i=u().join(process.cwd(),"uploads","temp");await l().mkdir(i,{recursive:!0});let d=Date.now(),m=`${d}_${r.name}`,f=u().join(i,m);await l().writeFile(f,c);try{let e=await p.processDocument(f,{detectFEMACategory:s,enableOCR:!0,outputFormat:"markdown"}),t=await g.document.create({data:{name:r.name,fileUrl:f,fileType:u().extname(r.name),size:r.size,extractedText:e.extractedText,confidence:e.confidence,femaCategory:e.femaCategory,processingStatus:e.success?"COMPLETED":"FAILED",userId:"temp-user",projectId:a||null}});return await l().unlink(f),n.Z.json({success:!0,document:{id:t.id,name:t.name,extractedText:e.extractedText,markdown:e.markdown,pages:e.pages,confidence:e.confidence,femaCategory:e.femaCategory,metadata:e.metadata}})}catch(e){try{await l().unlink(f)}catch(e){console.error("Failed to clean up temporary file:",e)}throw e}}catch(e){return console.error("Document processing error:",e),n.Z.json({error:"Failed to process document",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function PUT(e){try{let{filePaths:t,options:r}=await e.json();if(!Array.isArray(t)||0===t.length)return n.Z.json({error:"No file paths provided"},{status:400});let s=await p.processDocuments(t,{detectFEMACategory:r?.detectFEMA||!1,enableOCR:!0,outputFormat:"markdown"}),a=await Promise.all(s.map(async(e,s)=>{if(e.success)try{return await g.document.create({data:{name:u().basename(t[s]),fileUrl:t[s],fileType:e.metadata.fileType,size:e.metadata.fileSize,extractedText:e.extractedText,confidence:e.confidence,femaCategory:e.femaCategory,processingStatus:"COMPLETED",userId:"temp-user",projectId:r?.projectId||null}})}catch(e){console.error("Database error for file:",t[s],e)}return null}));return n.Z.json({success:!0,processed:s.length,successful:s.filter(e=>e.success).length,failed:s.filter(e=>!e.success).length,results:s.map((e,r)=>({filePath:t[r],success:e.success,extractedText:e.success?e.extractedText:null,femaCategory:e.success?e.femaCategory:null,error:e.success?null:e.error,documentId:a[r]?.id||null}))})}catch(e){return console.error("Batch processing error:",e),n.Z.json({error:"Failed to process documents batch",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/documents/process/route",pathname:"/api/documents/process",filename:"route",bundlePath:"app/api/documents/process/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\CBCS\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\June_2025-ComplianceMax\\ALL NEW APP\\app\\api\\documents\\process\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:x,serverHooks:C,headerHooks:h,staticGenerationBailout:T}=y,_="/api/documents/process/route"}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[955],()=>__webpack_exec__(4739));module.exports=r})();