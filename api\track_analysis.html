<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Analysis - ComplianceMax V74</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .tracking-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .status-header {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .progress-container {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .progress-bar {
            background: rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            height: 1.5rem;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #10b981, #34d399);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 1rem;
        }
        
        .step-list {
            display: grid;
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(75, 85, 99, 0.5);
        }
        
        .step-completed {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
        }
        
        .step-current {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.3);
        }
        
        .step-pending {
            background: rgba(75, 85, 99, 0.1);
            border-color: rgba(75, 85, 99, 0.3);
        }
        
        .step-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 2rem;
            text-align: center;
        }
        
        .contact-section {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 1rem;
            padding: 2rem;
            margin-top: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
        }
        
        .btn-secondary {
            background: rgba(75, 85, 99, 0.8);
            color: white;
            border: 1px solid rgba(156, 163, 175, 0.5);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="tracking-container">
        <!-- Header -->
        <div class="status-header">
            <h1 class="text-3xl font-bold mb-4">📊 Compliance Analysis Tracking</h1>
            <p class="text-xl text-blue-300">Analysis ID: <span id="analysisId">{{ analysis_id }}</span></p>
            <p class="text-lg text-gray-300">Professional FEMA Compliance Analysis Service</p>
        </div>
        
        <!-- Progress Section -->
        <div class="progress-container">
            <h2 class="text-2xl font-bold mb-4 text-green-400">📈 Analysis Progress</h2>
            
            <div class="progress-info mb-4">
                <p><strong>Current Status:</strong> <span id="currentStatus" class="text-yellow-400">Loading...</span></p>
                <p><strong>Current Step:</strong> <span id="currentStep" class="text-blue-300">Loading...</span></p>
                <p><strong>Estimated Completion:</strong> <span id="estimatedCompletion" class="text-green-300">Loading...</span></p>
            </div>
            
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill" style="width: 0%"></div>
            </div>
            <p class="text-center mt-2"><span id="progressPercent">0</span>% Complete</p>
        </div>
        
        <!-- Analysis Steps -->
        <div class="progress-container">
            <h2 class="text-2xl font-bold mb-4 text-blue-400">🔍 Analysis Steps</h2>
            
            <div class="step-list" id="stepsList">
                <!-- Steps will be populated by JavaScript -->
            </div>
        </div>
        
        <!-- What We're Analyzing -->
        <div class="progress-container">
            <h2 class="text-2xl font-bold mb-4 text-purple-400">📋 Analysis Scope</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="analysis-item">
                    <h3 class="text-lg font-semibold text-green-400">📜 Regulatory Analysis</h3>
                    <ul class="mt-2 text-gray-300">
                        <li>• Code of Federal Regulations (CFR)</li>
                        <li>• Robert T. Stafford Disaster Relief Act</li>
                        <li>• FEMA Policy Directives and Memos</li>
                        <li>• State and Local Requirements</li>
                    </ul>
                </div>
                <div class="analysis-item">
                    <h3 class="text-lg font-semibold text-blue-400">💼 Compliance Areas</h3>
                    <ul class="mt-2 text-gray-300">
                        <li>• Procurement Requirements</li>
                        <li>• Environmental Compliance</li>
                        <li>• Historic Preservation</li>
                        <li>• Cost-Effectiveness Analysis</li>
                    </ul>
                </div>
                <div class="analysis-item">
                    <h3 class="text-lg font-semibold text-yellow-400">⚖️ Legal Review</h3>
                    <ul class="mt-2 text-gray-300">
                        <li>• Statutory Requirements</li>
                        <li>• Regulatory Compliance</li>
                        <li>• Policy Interpretation</li>
                        <li>• Precedent Analysis</li>
                    </ul>
                </div>
                <div class="analysis-item">
                    <h3 class="text-lg font-semibold text-red-400">📊 Deliverables</h3>
                    <ul class="mt-2 text-gray-300">
                        <li>• Comprehensive PDF Report</li>
                        <li>• Executive Summary</li>
                        <li>• Action Items Checklist</li>
                        <li>• Regulatory Citations</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="progress-container">
            <h2 class="text-2xl font-bold mb-4 text-green-400">🎯 Available Actions</h2>
            <div class="text-center">
                <button onclick="refreshStatus()" class="btn btn-primary">🔄 Refresh Status</button>
                <button onclick="requestExpedited()" class="btn btn-secondary">⚡ Request Expedited Service</button>
                <button onclick="downloadReceipt()" class="btn btn-secondary">📄 Download Receipt</button>
                <a href="/dashboard" class="btn btn-secondary">🏠 Return to Dashboard</a>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="contact-section">
            <h2 class="text-2xl font-bold mb-4 text-indigo-400">📞 Need Assistance?</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="text-lg font-semibold mb-2">📧 Email Support</h3>
                    <p><EMAIL></p>
                    <p class="text-sm text-gray-400">Response within 4 hours</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-2">📱 Phone Support</h3>
                    <p>1-800-COMPLY-1</p>
                    <p class="text-sm text-gray-400">Business hours: 8 AM - 6 PM EST</p>
                </div>
            </div>
            <p class="mt-4 text-center text-gray-400">Reference your Analysis ID when contacting support</p>
        </div>
    </div>

    <script>
        const analysisId = '{{ analysis_id }}';
        
        // Load initial status
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalysisStatus();
            // Auto-refresh every 30 seconds
            setInterval(loadAnalysisStatus, 30000);
        });
        
        async function loadAnalysisStatus() {
            try {
                const response = await fetch(`/api/compliance-analysis/status/${analysisId}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    updateStatusDisplay(data.data);
                }
            } catch (error) {
                console.error('Error loading status:', error);
            }
        }
        
        function updateStatusDisplay(statusData) {
            // Update progress
            document.getElementById('currentStatus').textContent = statusData.status;
            document.getElementById('currentStep').textContent = statusData.current_step;
            document.getElementById('estimatedCompletion').textContent = statusData.estimated_completion;
            document.getElementById('progressPercent').textContent = statusData.progress;
            document.getElementById('progressFill').style.width = statusData.progress + '%';
            
            // Update steps
            updateStepsList(statusData);
        }
        
        function updateStepsList(statusData) {
            const stepsList = document.getElementById('stepsList');
            stepsList.innerHTML = '';
            
            // Add completed steps
            statusData.steps_completed.forEach(step => {
                const stepDiv = document.createElement('div');
                stepDiv.className = 'step-item step-completed';
                stepDiv.innerHTML = `
                    <div class="step-icon">✅</div>
                    <div>
                        <strong>${step}</strong>
                        <p class="text-sm text-gray-400">Completed</p>
                    </div>
                `;
                stepsList.appendChild(stepDiv);
            });
            
            // Add current step
            const currentStepDiv = document.createElement('div');
            currentStepDiv.className = 'step-item step-current';
            currentStepDiv.innerHTML = `
                <div class="step-icon">🔄</div>
                <div>
                    <strong>${statusData.current_step}</strong>
                    <p class="text-sm text-yellow-400">In Progress</p>
                </div>
            `;
            stepsList.appendChild(currentStepDiv);
            
            // Add remaining steps
            statusData.steps_remaining.forEach(step => {
                const stepDiv = document.createElement('div');
                stepDiv.className = 'step-item step-pending';
                stepDiv.innerHTML = `
                    <div class="step-icon">⏳</div>
                    <div>
                        <strong>${step}</strong>
                        <p class="text-sm text-gray-400">Pending</p>
                    </div>
                `;
                stepsList.appendChild(stepDiv);
            });
        }
        
        function refreshStatus() {
            loadAnalysisStatus();
            alert('Status refreshed!');
        }
        
        function requestExpedited() {
            alert(`⚡ Expedited Service Request\n\nAnalysis ID: ${analysisId}\n\nOur team will contact you within 2 hours to discuss expedited analysis options.\n\nExpedited service typically delivers results within 24 hours.`);
        }
        
        function downloadReceipt() {
            const receipt = {
                analysis_id: analysisId,
                timestamp: new Date().toISOString(),
                service: 'FEMA Compliance Analysis',
                status: 'In Progress'
            };
            
            const blob = new Blob([JSON.stringify(receipt, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ComplianceMax_Receipt_${analysisId}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html> 