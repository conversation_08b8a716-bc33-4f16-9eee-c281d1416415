const spawn = require('cross-spawn');
const path = require('path');

class PodExecutor {
  constructor(baseDir) {
    this.baseDir = baseDir;
    console.log('🤖 Compliance Pod Executor initialized');
    console.log(`📁 Base directory: ${this.baseDir}`);
  }

  execute(task, args, cwd) {
    const fullPath = path.join(this.baseDir, cwd);
    console.log(`🔄 Executing: ${task} ${args.join(' ')}`);
    console.log(`📂 Working directory: ${fullPath}`);
    
    const result = spawn.sync(task, args, { 
      cwd: fullPath, 
      stdio: 'inherit',
      shell: true 
    });
    
    if (result.error) {
      throw new Error(`❌ Task ${task} failed: ${result.error.message}`);
    }
    
    console.log(`✅ Task ${task} completed successfully`);
  }

  startFrontend() {
    console.log('\n🚀 Starting Frontend (Next.js)...');
    this.execute('npm', ['run', 'dev'], 'app');
  }

  startBackend() {
    console.log('\n🚀 Starting Backend (FastAPI)...');
    this.execute('python', ['-m', 'uvicorn', 'main:app', '--host', '0.0.0.0', '--port', '8000', '--reload'], 'api');
  }

  runScript(scriptName) {
    console.log(`\n🔧 Running script: ${scriptName}...`);
    this.execute('node', [scriptName], 'scripts');
  }

  installDependencies() {
    console.log('\n📦 Installing Frontend Dependencies...');
    this.execute('npm', ['install'], 'app');
    
    console.log('\n📦 Installing Backend Dependencies...');
    this.execute('pip', ['install', 'uvicorn', 'fastapi'], 'api');
  }

  verifyServers() {
    console.log('\n🔍 Verifying server status...');
    this.execute('node', ['verify-servers.js'], '.');
  }
}

// Usage examples
if (require.main === module) {
  const pod = new PodExecutor(__dirname);
  
  // Example: Install dependencies and start servers
  try {
    pod.installDependencies();
    // pod.startFrontend();  // Uncomment to start frontend
    // pod.startBackend();   // Uncomment to start backend
    // pod.verifyServers();  // Uncomment to verify after starting
  } catch (error) {
    console.error('💥 Pod execution failed:', error.message);
    process.exit(1);
  }
}

module.exports = PodExecutor; 