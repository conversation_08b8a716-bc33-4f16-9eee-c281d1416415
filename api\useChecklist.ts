import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then((r) => r.json());

export function useChecklist(version?: string, phase?: number) {
  const params = new URLSearchParams();
  if (version) params.append('version', version);
  if (phase !== undefined) params.append('phase', String(phase));
  const key = `/api/checklist?${params.toString()}`;
  const { data, error, isLoading } = useSWR(key, fetcher);
  return {
    items: data?.items ?? [],
    count: data?.count ?? 0,
    isLoading,
    error,
  };
} 