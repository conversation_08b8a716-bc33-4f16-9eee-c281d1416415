<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax V74 - Professional Intake System</title>
    <style>[\s\S]*?</style>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/professional_intake.css') }}">
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>📋 CBCS Professional Analysis Service</h1>
                <div class="subtitle">Category C - Roads and Bridges Intake</div>
            </div>
            <div class="badges">
                <span class="badge policy">PAPPG v5.0</span>
                <span class="badge database">53,048 Records</span>
                <span class="badge cbcs">DRRA 1235b</span>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Left Column: CBCS Codes Selection -->
            <div class="column">
                <h3>🔧 CBCS Codes & Standards Selection</h3>
                
                <div class="cbcs-codes">
                    <h4>AASHTO Standards (Required for Roads/Bridges)</h4>
                    <div class="code-item">
                        <input type="checkbox" id="aashto_lrfd" name="cbcs_codes" value="AASHTO_LRFD">
                        <label for="aashto_lrfd">
                            <div class="code-title">AASHTO LRFD Bridge Design Specifications</div>
                            <div class="code-desc">Load and Resistance Factor Design for highway bridges</div>
                        </label>
                    </div>
                    <div class="code-item">
                        <input type="checkbox" id="aashto_geometric" name="cbcs_codes" value="AASHTO_Geometric">
                        <label for="aashto_geometric">
                            <div class="code-title">AASHTO Policy on Geometric Design of Highways</div>
                            <div class="code-desc">Highway and street geometric design standards</div>
                        </label>
                    </div>
                    <div class="code-item">
                        <input type="checkbox" id="aashto_seismic" name="cbcs_codes" value="AASHTO_Seismic">
                        <label for="aashto_seismic">
                            <div class="code-title">AASHTO Guide Specifications for LRFD Seismic Bridge Design</div>
                            <div class="code-desc">Seismic design requirements for bridge structures</div>
                        </label>
                    </div>
                </div>
                
                <div class="cbcs-codes">
                    <h4>Structural Engineering Standards</h4>
                    <div class="code-item">
                        <input type="checkbox" id="asce_7" name="cbcs_codes" value="ASCE_7">
                        <label for="asce_7">
                            <div class="code-title">ASCE 7-16 Minimum Design Loads</div>
                            <div class="code-desc">Wind, seismic, snow, and other load requirements</div>
                        </label>
                    </div>
                    <div class="code-item">
                        <input type="checkbox" id="aci_318" name="cbcs_codes" value="ACI_318">
                        <label for="aci_318">
                            <div class="code-title">ACI 318-19 Building Code Requirements for Reinforced Concrete</div>
                            <div class="code-desc">Concrete design and construction standards</div>
                        </label>
                    </div>
                    <div class="code-item">
                        <input type="checkbox" id="aisc_360" name="cbcs_codes" value="AISC_360">
                        <label for="aisc_360">
                            <div class="code-title">AISC 360-19 Specification for Structural Steel Buildings</div>
                            <div class="code-desc">Steel structure design specifications</div>
                        </label>
                    </div>
                </div>
                
                <div class="cbcs-codes">
                    <h4>Safety & Accessibility</h4>
                    <div class="code-item">
                        <input type="checkbox" id="nfpa_1141" name="cbcs_codes" value="NFPA_1141">
                        <label for="nfpa_1141">
                            <div class="code-title">NFPA 1141 Fire Protection Infrastructure</div>
                            <div class="code-desc">Fire protection for land development projects</div>
                        </label>
                    </div>
                    <div class="code-item">
                        <input type="checkbox" id="ada_2010" name="cbcs_codes" value="ADA_2010">
                        <label for="ada_2010">
                            <div class="code-title">ADA 2010 Standards for Accessible Design</div>
                            <div class="code-desc">Accessibility compliance for public facilities</div>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Center Column: Project Details & Hazards -->
            <div class="column">
                <h3>📄 Document Upload & Project Details</h3>
                
                <div class="form-group">
                    <label class="form-label">Upload Technical Documents</label>
                    <div class="upload-zone" onclick="document.getElementById('fileInput').click()">
                        <div class="icon">📁</div>
                        <div><strong>Click to upload or drag files here</strong></div>
                        <div class="info-text">PDF, DWG, DOC, XLS files up to 50MB each</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.dwg,.doc,.docx,.xls,.xlsx" style="display: none;">
                    <div class="info-text">Upload engineering reports, architectural drawings, code analysis, or other technical documents</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Hazard-Specific Requirements *</label>
                    <div class="hazard-grid">
                        <div class="hazard-item">
                            <input type="checkbox" id="seismic" name="hazards" value="seismic">
                            <label for="seismic">🌍 Seismic Design</label>
                        </div>
                        <div class="hazard-item">
                            <input type="checkbox" id="flood" name="hazards" value="flood">
                            <label for="flood">🌊 Flood Resistant</label>
                        </div>
                        <div class="hazard-item">
                            <input type="checkbox" id="wind" name="hazards" value="wind">
                            <label for="wind">💨 High Wind</label>
                        </div>
                        <div class="hazard-item">
                            <input type="checkbox" id="coastal" name="hazards" value="coastal">
                            <label for="coastal">🏖️ Coastal Environment</label>
                        </div>
                        <div class="hazard-item">
                            <input type="checkbox" id="wildfire" name="hazards" value="wildfire">
                            <label for="wildfire">🔥 Wildfire Protection</label>
                        </div>
                        <div class="hazard-item">
                            <input type="checkbox" id="snow" name="hazards" value="snow">
                            <label for="snow">❄️ Snow/Ice Load</label>
                        </div>
                    </div>
                    <div class="info-text">Select all hazard-resistant design criteria applicable to your project location</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Professional Engineering Certification Required? *</label>
                    <div style="margin-top: 10px;">
                        <div class="hazard-item" style="margin-bottom: 8px;">
                            <input type="radio" id="pe_yes" name="pe_cert" value="yes" required>
                            <label for="pe_yes">✅ Yes - PE certification required</label>
                        </div>
                        <div class="hazard-item">
                            <input type="radio" id="pe_no" name="pe_cert" value="no">
                            <label for="pe_no">❌ No - Not required</label>
                        </div>
                    </div>
                    <div class="info-text">Professional engineer certification for CBCS compliance verification</div>
                </div>
                
                <div class="form-group">
                    <label for="local_codes" class="form-label">Local Code Variations</label>
                    <textarea id="local_codes" name="local_codes" class="form-control" rows="4" 
                             placeholder="Describe any local codes or ordinances that are more stringent than the selected CBCS requirements..."></textarea>
                    <div class="info-text">Local requirements that exceed consensus-based standards</div>
                </div>
            </div>
            
            <!-- Right Column: Justification & Additional Info -->
            <div class="column">
                <h3>📝 Code Selection Justification</h3>
                
                <div class="form-group">
                    <label for="justification" class="form-label">Automatic Technical Justification</label>
                    <textarea id="justification" name="justification" class="form-control justification-area" readonly>Based on your Category C - Roads and Bridges project selection, the following consensus-based codes and standards are applicable per DRRA Section 1235b:

PLACEHOLDER JUSTIFICATION - Select codes to generate detailed technical explanations.

The system will automatically generate comprehensive justifications including:
• Technical applicability to your specific project type
• Hazard-resistant design criteria and benefits  
• Compliance requirements and implementation guidance
• Long-term resilience and risk reduction benefits
• Cost-benefit analysis for FEMA funding eligibility

Select applicable codes from the left panel to generate customized boilerplate language that demonstrates compliance with DRRA Section 1235b requirements.</textarea>
                    <button type="button" class="btn btn-generate" onclick="generateBoilerplate()">
                        🔄 Generate Technical Justification
                    </button>
                    <div class="info-text">System-generated explanations based on your code selections and project details</div>
                </div>
                
                <div class="form-group">
                    <label for="special_circumstances" class="form-label">Special Circumstances or Technical Challenges</label>
                    <textarea id="special_circumstances" name="special_circumstances" class="form-control" rows="5" 
                             placeholder="Describe any special circumstances, historic preservation requirements, environmental constraints, or technical challenges that may affect CBCS implementation..."></textarea>
                    <div class="info-text">Historic preservation, environmental factors, or unique technical considerations</div>
                </div>
                
                <div class="form-group">
                    <label for="project_description" class="form-label">Detailed Work Description</label>
                    <textarea id="project_description" name="project_description" class="form-control" rows="4" 
                             placeholder="Provide detailed description of the work to be performed, including scope, materials, and specifications..."></textarea>
                    <div class="info-text">Comprehensive project scope for accurate code applicability assessment</div>
                </div>
                
                <button type="button" class="btn btn-submit" onclick="submitIntake()">
                    SUBMIT FOR PROFESSIONAL ANALYSIS AND RECOMMENDATIONS
                </button>
            </div>
        </div>
    </div>

    <script>
        // Generate boilerplate based on selected codes
        function generateBoilerplate() {
            const selectedCodes = Array.from(document.querySelectorAll('input[name="cbcs_codes"]:checked')).map(cb => cb.value);
            const selectedHazards = Array.from(document.querySelectorAll('input[name="hazards"]:checked')).map(cb => cb.value);
            
            if (selectedCodes.length === 0) {
                alert('Please select at least one CBCS code to generate justification.');
                return;
            }
            
            let justification = `TECHNICAL JUSTIFICATION FOR CBCS COMPLIANCE\nCategory C - Roads and Bridges Project\nDRRA Section 1235b Requirements\n\n`;
            
            // Generate justification for each selected code
            selectedCodes.forEach(code => {
                switch(code) {
                    case 'AASHTO_LRFD':
                        justification += `AASHTO LRFD Bridge Design Specifications:\n`;
                        justification += `• Applicable to all highway bridge structures in this project\n`;
                        justification += `• Provides load and resistance factor design methodology\n`;
                        justification += `• Incorporates latest seismic and wind load requirements\n`;
                        justification += `• Required for FEMA funding eligibility per DRRA 1235b\n\n`;
                        break;
                    case 'AASHTO_Geometric':
                        justification += `AASHTO Policy on Geometric Design of Highways:\n`;
                        justification += `• Establishes minimum design standards for roadway geometry\n`;
                        justification += `• Ensures safety and operational efficiency\n`;
                        justification += `• Applicable to all roadway reconstruction activities\n`;
                        justification += `• Compliance required for federal funding eligibility\n\n`;
                        break;
                    case 'ASCE_7':
                        justification += `ASCE 7-16 Minimum Design Loads:\n`;
                        justification += `• Defines wind, seismic, snow, and other environmental loads\n`;
                        justification += `• Critical for structural design and safety\n`;
                        justification += `• Incorporates latest hazard mapping and risk factors\n`;
                        justification += `• Required for all structural elements in the project\n\n`;
                        break;
                    // Add more cases for other codes...
                }
            });
            
            // Add hazard-specific information
            if (selectedHazards.length > 0) {
                justification += `HAZARD-SPECIFIC DESIGN REQUIREMENTS:\n`;
                selectedHazards.forEach(hazard => {
                    switch(hazard) {
                        case 'seismic':
                            justification += `• Seismic Design: ASCE 7 seismic provisions and AASHTO seismic bridge design specifications apply\n`;
                            break;
                        case 'flood':
                            justification += `• Flood Resistance: ASCE 24 flood resistant design and construction requirements apply\n`;
                            break;
                        case 'wind':
                            justification += `• High Wind Design: ASCE 7 wind load provisions for extreme wind events apply\n`;
                            break;
                        case 'coastal':
                            justification += `• Coastal Environment: Additional corrosion protection and wave load considerations apply\n`;
                            break;
                    }
                });
                justification += `\n`;
            }
            
            justification += `COMPLIANCE BENEFITS:\n`;
            justification += `• Enhanced structural resilience and reduced future damage\n`;
            justification += `• Compliance with federal funding requirements\n`;
            justification += `• Improved public safety and facility performance\n`;
            justification += `• Long-term cost savings through hazard-resistant design\n`;
            justification += `• Eligibility for FEMA Public Assistance funding per DRRA Section 1235b`;
            
            document.getElementById('justification').value = justification;
        }
        
        // Submit intake form
        function submitIntake() {
            const selectedCodes = Array.from(document.querySelectorAll('input[name="cbcs_codes"]:checked')).map(cb => cb.value);
            const selectedHazards = Array.from(document.querySelectorAll('input[name="hazards"]:checked')).map(cb => cb.value);
            const peCert = document.querySelector('input[name="pe_cert"]:checked');
            
            if (selectedCodes.length === 0) {
                alert('Please select at least one CBCS code before submitting.');
                return;
            }
            
            if (!peCert) {
                alert('Please specify if professional engineering certification is required.');
                return;
            }
            
            const formData = {
                cbcs_codes: selectedCodes,
                hazards: selectedHazards,
                pe_certification: peCert.value,
                local_codes: document.getElementById('local_codes').value,
                special_circumstances: document.getElementById('special_circumstances').value,
                project_description: document.getElementById('project_description').value,
                justification: document.getElementById('justification').value
            };
            
            // Show loading state
            const submitBtn = document.querySelector('.btn-submit');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'SUBMITTING...';
            submitBtn.disabled = true;
            
            // Simulate submission (replace with actual API call)
            setTimeout(() => {
                alert('Professional intake submitted successfully! You will receive a comprehensive analysis within 2-3 business days.');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }
        
        // File upload handling
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            const uploadZone = document.querySelector('.upload-zone');
            
            if (files.length > 0) {
                uploadZone.innerHTML = `
                    <div class="icon">✅</div>
                    <div><strong>${files.length} file(s) selected</strong></div>
                    <div class="info-text">${files.map(f => f.name).join(', ')}</div>
                `;
            }
        });
    </script>
</body>
</html> 