# ParaDocs Python Requirements
# Core dependencies for document search and management

# Standard library enhancements
python-dateutil>=2.8.2

# Document processing
openpyxl>=3.1.2          # Excel file reading/writing
PyPDF2>=3.0.1            # PDF text extraction
python-docx>=0.8.11      # Word document processing

# Database (future implementation)
# sqlalchemy>=2.0.0      # ORM for database operations

# Web framework (future implementation)
# fastapi>=0.100.0       # Modern web API framework
# uvicorn>=0.23.0        # ASGI server
# python-multipart>=0.0.6 # Form data handling

# Search enhancements (future implementation)
# whoosh>=2.7.4          # Pure Python search engine
# fuzzywuzzy>=0.18.0     # Fuzzy string matching
# python-Levenshtein>=0.21.0  # Speed up fuzzy matching

# Security (future implementation)
# cryptography>=41.0.0   # Encryption for sensitive data
# python-jose>=3.3.0     # JWT tokens

# Development tools
# pytest>=7.4.0          # Testing framework
# black>=23.0.0          # Code formatting
# pylint>=2.17.0         # Code linting

# Optional: Data analysis
# pandas>=2.0.0          # Data manipulation
# matplotlib>=3.7.0      # Visualization 