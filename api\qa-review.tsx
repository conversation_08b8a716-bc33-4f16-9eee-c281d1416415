"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  CheckCircle2, 
  <PERSON><PERSON><PERSON>Che<PERSON>, 
  ThumbsDown, 
  XCircle 
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { getProject } from "@/lib/data-access"

interface QAReviewProps {
  projectId: string
}

export function QAReview({ projectId }: QAReviewProps) {
  const [status, setStatus] = useState<string>("PENDING")
  const [comments, setComments] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [project, setProject] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  
  useEffect(() => {
    async function loadProject() {
      try {
        const projectData = await getProject(projectId)
        setProject(projectData)
      } catch (error) {
        console.error("Error loading project:", error)
      } finally {
        setLoading(false)
      }
    }
    
    loadProject()
  }, [projectId])

  const handleSubmit = () => {
    setIsSubmitting(true)
    
    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Review submitted",
        description: `The project has been marked as ${status.toLowerCase().replace("_", " ")}.`,
      })
      setIsSubmitting(false)
    }, 1500)
  }

  const getStatusColor = () => {
    switch (status) {
      case "APPROVED":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
      case "REJECTED":
        return "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
      case "NEEDS_REVISION":
        return "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800"
      default:
        return "bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case "APPROVED":
        return <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
      case "REJECTED":
        return <ThumbsDown className="h-4 w-4 text-red-600 dark:text-red-400" />
      case "NEEDS_REVISION":
        return <XCircle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
      default:
        return <ClipboardCheck className="h-4 w-4 text-gray-600 dark:text-gray-400" />
    }
  }

  if (loading) return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
      <p className="text-center text-gray-500 dark:text-gray-400">Loading project data...</p>
    </div>
  )
  
  if (!project) return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
      <p className="text-center text-gray-500 dark:text-gray-400">Project not found</p>
    </div>
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6"
    >
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 font-heading">
        Quality Assurance Review
      </h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3 font-heading">
            Review Status
          </h3>
          <div className="flex flex-wrap gap-3">
            <StatusButton
              status="APPROVED"
              currentStatus={status}
              onClick={() => setStatus("APPROVED")}
              icon={<CheckCircle2 className="h-4 w-4 mr-2" />}
              label="Approve"
              color="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800"
              activeColor="bg-green-600 text-white border-green-600"
            />
            <StatusButton
              status="REJECTED"
              currentStatus={status}
              onClick={() => setStatus("REJECTED")}
              icon={<ThumbsDown className="h-4 w-4 mr-2" />}
              label="Reject"
              color="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800"
              activeColor="bg-red-600 text-white border-red-600"
            />
            <StatusButton
              status="NEEDS_REVISION"
              currentStatus={status}
              onClick={() => setStatus("NEEDS_REVISION")}
              icon={<XCircle className="h-4 w-4 mr-2" />}
              label="Needs Revision"
              color="bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 border-orange-200 dark:border-orange-800"
              activeColor="bg-orange-600 text-white border-orange-600"
            />
            <StatusButton
              status="PENDING"
              currentStatus={status}
              onClick={() => setStatus("PENDING")}
              icon={<ClipboardCheck className="h-4 w-4 mr-2" />}
              label="Pending"
              color="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600"
              activeColor="bg-gray-600 text-white border-gray-600"
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3 font-heading">
            Review Comments
          </h3>
          <div className={`p-4 rounded-lg border ${getStatusColor()}`}>
            <div className="flex items-center mb-3">
              <div className="p-1.5 bg-white dark:bg-gray-700 rounded-full mr-2">
                {getStatusIcon()}
              </div>
              <span className="text-sm font-medium">
                {status === "PENDING" ? "Pending Review" : 
                 status === "APPROVED" ? "Approved" : 
                 status === "REJECTED" ? "Rejected" : 
                 "Needs Revision"}
              </span>
            </div>
            <Textarea
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Enter your review comments here..."
              className="min-h-[150px] resize-none"
            />
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="gradient-button"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Submitting...
              </div>
            ) : (
              <>
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Submit Review
              </>
            )}
          </Button>
        </div>
      </div>
    </motion.div>
  )
}

interface StatusButtonProps {
  status: string
  currentStatus: string
  onClick: () => void
  icon: React.ReactNode
  label: string
  color: string
  activeColor: string
}

function StatusButton({ status, currentStatus, onClick, icon, label, color, activeColor }: StatusButtonProps) {
  const isActive = status === currentStatus
  
  return (
    <button
      type="button"
      onClick={onClick}
      className={`px-4 py-2 rounded-md border text-sm font-medium flex items-center transition-colors ${
        isActive ? activeColor : color
      }`}
    >
      {icon}
      {label}
    </button>
  )
}