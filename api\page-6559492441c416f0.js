(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[479],{3111:function(e,t,s){Promise.resolve().then(s.bind(s,8416))},8416:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return DocumentsPage}});var r=s(7437),a=s(2265),n=s(3835),l=s(1317),i=s(7171),c=s(8647),d=s(7430),o=s(8915),m=s(6794),x=s(4115),u=s(1666),f=s(1023),p=s(4862),g=s(9598),h=s(3611),v=s(708),j=s(877),b=s(1810),N=s(1628);let y=b.fC,w=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(b.aV,{ref:t,className:(0,N.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});w.displayName=b.aV.displayName;let C=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(b.xz,{ref:t,className:(0,N.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});C.displayName=b.xz.displayName;let k=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(b.VY,{ref:t,className:(0,N.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});k.displayName=b.VY.displayName;var D=s(6908),S=s(3408);let T=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(D.fC,{ref:t,className:(0,N.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...a,children:(0,r.jsx)(D.z$,{className:(0,N.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(S.Z,{className:"h-4 w-4"})})})});T.displayName=D.fC.displayName;var P=s(6061);let z=(0,P.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),E=a.forwardRef((e,t)=>{let{className:s,variant:a,...n}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,N.cn)(z({variant:a}),s),...n})});E.displayName="Alert";let Z=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h5",{ref:t,className:(0,N.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})});Z.displayName="AlertTitle";let M=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,N.cn)("text-sm [&_p]:leading-relaxed",s),...a})});function DocumentProcessor(e){let{onDocumentProcessed:t,projectId:s,allowMultiple:n=!0}=e,[l,b]=(0,a.useState)(!1),[N,D]=(0,a.useState)([]),[S,P]=(0,a.useState)(0),[z,Z]=(0,a.useState)(!0),[R,A]=(0,a.useState)(null),[F,V]=(0,a.useState)(null),O=(0,a.useCallback)(async e=>{if(0!==e.length){b(!0),A(null),P(0);try{let r=[];for(let a=0;a<e.length;a++){let n=e[a];P(a/e.length*100);let l=new FormData;l.append("file",n),l.append("detectFEMA",z.toString()),s&&l.append("projectId",s);let i=await fetch("/api/documents/process",{method:"POST",body:l});if(!i.ok){let e=await i.json();throw Error(e.error||"Failed to process document")}let c=await i.json();c.success&&(r.push(c.document),null==t||t(c.document))}D(e=>[...e,...r]),P(100),setTimeout(()=>P(0),2e3)}catch(e){console.error("Document processing error:",e),A(e instanceof Error?e.message:"Failed to process documents")}finally{b(!1)}}},[z,s,t]),{getRootProps:_,getInputProps:B,isDragActive:I}=(0,c.uI)({onDrop:O,multiple:n,accept:{"image/*":[".jpg",".jpeg",".png",".gif",".tiff",".bmp"],"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":[".xlsx"],"text/html":[".html"],"text/markdown":[".md"],"text/plain":[".txt"]},disabled:l}),removeDocument=e=>{D(t=>t.filter(t=>t.id!==e)),(null==F?void 0:F.id)===e&&V(null)},getConfidenceColor=e=>e>=.8?"text-green-600":e>=.6?"text-yellow-600":"text-red-600",getFEMACategoryColor=e=>e&&"Uncategorized"!==e?"default":"secondary";return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(g.Zb,{children:[(0,r.jsx)(g.Ol,{children:(0,r.jsxs)(g.ll,{className:"flex items-center gap-2",children:[(0,r.jsx)(o.Z,{className:"h-5 w-5"}),"Document Processor"]})}),(0,r.jsxs)(g.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(T,{id:"fema-detection",checked:z,onCheckedChange:e=>Z(e)}),(0,r.jsx)("label",{htmlFor:"fema-detection",className:"text-sm font-medium",children:"Auto-detect FEMA categories"})]}),(0,r.jsxs)("div",{..._(),className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer\n              ".concat(I?"border-blue-500 bg-blue-50 dark:bg-blue-950":"border-gray-300 hover:border-gray-400","\n              ").concat(l?"opacity-50 cursor-not-allowed":""),children:[(0,r.jsx)("input",{...B()}),(0,r.jsxs)("div",{className:"space-y-4",children:[l?(0,r.jsx)(m.Z,{className:"h-8 w-8 animate-spin mx-auto text-blue-500"}):(0,r.jsx)(o.Z,{className:"h-8 w-8 mx-auto text-gray-400"}),(0,r.jsx)("div",{children:l?(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Processing documents..."}):I?(0,r.jsx)("p",{className:"text-sm text-blue-600",children:"Drop files here to process"}):(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Drag & drop documents here, or click to select"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Supports: PDF, Images, DOCX, PPTX, XLSX, HTML, MD, TXT"})]})})]})]}),S>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Processing..."}),(0,r.jsxs)("span",{children:[Math.round(S),"%"]})]}),(0,r.jsx)(j.E,{value:S,className:"h-2"})]}),R&&(0,r.jsxs)(E,{variant:"destructive",children:[(0,r.jsx)(x.Z,{className:"h-4 w-4"}),(0,r.jsx)(M,{children:R})]})]})]}),N.length>0&&(0,r.jsxs)(g.Zb,{children:[(0,r.jsx)(g.Ol,{children:(0,r.jsxs)(g.ll,{className:"flex items-center gap-2",children:[(0,r.jsx)(i.Z,{className:"h-5 w-5"}),"Processed Documents (",N.length,")"]})}),(0,r.jsx)(g.aY,{children:(0,r.jsxs)(y,{defaultValue:"list",className:"w-full",children:[(0,r.jsxs)(w,{children:[(0,r.jsx)(C,{value:"list",children:"List View"}),(0,r.jsx)(C,{value:"details",children:"Detailed View"})]}),(0,r.jsx)(k,{value:"list",className:"space-y-3",children:N.map(e=>(0,r.jsxs)(d.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"border rounded-lg p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(u.Z,{className:"h-5 w-5 text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:[e.pages," pages"]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{className:getConfidenceColor(e.confidence),children:[Math.round(100*e.confidence),"% confidence"]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:[Math.round(e.metadata.fileSize/1024)," KB"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.femaCategory&&(0,r.jsx)(v.C,{variant:getFEMACategoryColor(e.femaCategory),children:e.femaCategory}),(0,r.jsx)(h.z,{variant:"outline",size:"sm",onClick:()=>V(e),children:(0,r.jsx)(f.Z,{className:"h-4 w-4"})}),(0,r.jsx)(h.z,{variant:"outline",size:"sm",onClick:()=>removeDocument(e.id),children:(0,r.jsx)(p.Z,{className:"h-4 w-4"})})]})]}),e.extractedText&&(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded p-3",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Extracted Text Preview:"}),(0,r.jsxs)("p",{className:"text-sm line-clamp-3",children:[e.extractedText.substring(0,200),"..."]})]})]},e.id))}),(0,r.jsx)(k,{value:"details",children:F?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:F.name}),(0,r.jsx)(h.z,{variant:"outline",onClick:()=>V(null),children:"Close"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:F.pages}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Pages"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold ".concat(getConfidenceColor(F.confidence)),children:[Math.round(100*F.confidence),"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Confidence"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:Math.round(F.metadata.fileSize/1024)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"KB"})]}),(0,r.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:F.metadata.processingTime}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"ms"})]})]}),F.femaCategory&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"FEMA Category"}),(0,r.jsx)(v.C,{variant:getFEMACategoryColor(F.femaCategory),children:F.femaCategory})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Extracted Text"}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded p-4 max-h-64 overflow-y-auto",children:(0,r.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:F.extractedText})})]})]}):(0,r.jsx)("p",{className:"text-center text-gray-500 py-8",children:"Select a document from the list to view details"})})]})})]})]})}function DocumentsPage(){let[e,t]=(0,a.useState)([]);return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Documents"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Upload and process documents with AI-powered text extraction and FEMA categorization"})]}),(0,r.jsx)("div",{className:"flex gap-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search documents...",className:"pl-10 pr-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"})]})})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(l.Z,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"AI Document Processor"}),(0,r.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:"Powered by Docling"})]}),(0,r.jsx)(DocumentProcessor,{onDocumentProcessed:e=>{console.log("Document processed:",e)},allowMultiple:!0})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Document Library"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsx)("div",{className:"border rounded p-4 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(i.Z,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:"Sample Document"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Last modified: 2024-02-21"}),(0,r.jsxs)("div",{className:"mt-2 flex gap-2",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded",children:"Category B"}),(0,r.jsx)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:"95% confidence"})]})]})]})}),e.map(e=>(0,r.jsx)("div",{className:"border rounded p-4 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(i.Z,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.pages," pages • Just processed"]}),(0,r.jsxs)("div",{className:"mt-2 flex gap-2",children:[e.femaCategory&&(0,r.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded",children:e.femaCategory}),(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:[Math.round(100*e.confidence),"% confidence"]})]}),e.extractedText&&(0,r.jsxs)("p",{className:"text-xs text-gray-600 mt-2 line-clamp-2",children:[e.extractedText.substring(0,100),"..."]})]})]})},e.id))]}),0===e.length&&(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(i.Z,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,r.jsx)("p",{children:"No documents in library yet. Upload documents above to get started."})]})]})]})}M.displayName="AlertDescription"},708:function(e,t,s){"use strict";s.d(t,{C:function(){return Badge}});var r=s(7437);s(2265);var a=s(6061),n=s(1628);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Badge(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...a})}},3611:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(7437),a=s(2265),n=s(6061),l=s(1628);let i=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:c=!1,...d}=e;return(0,r.jsx)("button",{className:(0,l.cn)(i({variant:a,size:n,className:s})),ref:t,...d})});c.displayName="Button"},9598:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},Zb:function(){return l},aY:function(){return o},ll:function(){return c}});var r=s(7437),a=s(2265),n=s(1628);let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm transition-all hover:shadow-md card-hover-effect",s),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight font-heading",s),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent";let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})});m.displayName="CardFooter"},877:function(e,t,s){"use strict";s.d(t,{E:function(){return i}});var r=s(7437),a=s(2265),n=s(6828),l=s(1628);let i=a.forwardRef((e,t)=>{let{className:s,value:a,indicatorClassName:i,...c}=e;return(0,r.jsx)(n.fC,{ref:t,className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...c,children:(0,r.jsx)(n.z$,{className:(0,l.cn)("h-full w-full flex-1 bg-primary transition-all",i),style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});i.displayName=n.fC.displayName},1628:function(e,t,s){"use strict";s.d(t,{cn:function(){return cn},o0:function(){return formatDateTime},z2:function(){return getStatusColor}});var r=s(348),a=s(3986);function cn(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m)((0,r.W)(t))}function formatDateTime(e){let t=new Date(e);return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"numeric",minute:"numeric"}).format(t)}function getStatusColor(e){switch(null==e?void 0:e.toLowerCase()){case"approved":return"success";case"pending":return"warning";case"rejected":return"destructive";case"in progress":return"secondary";default:return"default"}}}},function(e){e.O(0,[100,735,617,494,971,472,744],function(){return e(e.s=3111)}),_N_E=e.O()}]);