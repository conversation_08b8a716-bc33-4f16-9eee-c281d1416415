# ComplianceMax Refactor & Delivery Roadmap

_Last updated: 2025-05-22 00:00 UTC_

## Objective
Deliver a single, clean, production-ready Next.js 14 codebase for **ComplianceMax** that reproduces the UI/UX shown in the Abacus screenshots while leveraging advanced features already built (document processing, OCR, validation, review workflows, and reporting) to ensure a comprehensive, maintainable, secure compliance management system.

---

## Phase 0 – Codebase Analysis & Selection (COMPLETED)
1. Inventory existing candidate folders:
   - `MORE ZIP FILES`
   - `MORE ZIP FILES 2-WIZRDS`
   - `MORE ZIP FILES 3`
   - `MORE ZIP FILES-05-19-25`
   - Express application (`express-test`)
   - Clean Next.js application (`clean-app`)
   - Main app directory with advanced features (`app`)
2. For each folder:
   - Run `npm i` (root) → `npm run dev`.
   - Check for **nested workspace** (`global_package.json` / `workspaces`) and install if present.
3. Smoke-test pages-of-record (Home ▸ Dashboard ▸ Projects ▸ Wizard ▸ Documents).
4. Score each candidate (✅ works / ⚠️ minor issues / ❌ major blockers):
   | Folder | Build | Console Errors | Functional parity | Duplicates? | Comments |
   | ------ | ----- | -------------- | ----------------- | ----------- | -------- |
   | MORE ZIP FILES | ⚠️ | minor | partial | yes | Contains useful components |
   | MORE ZIP FILES 2-WIZRDS | ⚠️ | minor | partial | yes | Contains useful wizard logic |
   | MORE ZIP FILES 3 | ⚠️ *static analysis* | minor? | template-only (basic UI) | yes (duplicate code paths) | Likely older snapshot |
   | MORE ZIP FILES-05-19-25 | ✅ (deps fixed) | none | FULL parity | yes (src vs app duplicate) | **GOLD SOURCE** – installed react-chartjs-2, chart.js; downgraded framer-motion→11.10.0 |
   | express-test | ✅ | none | functional UI | no | Complete Express-based solution |
   | clean-app | ⚠️ | minor | incomplete | no | Next.js App Router shell |
   | app | ✅ | none | advanced features | no | Contains document processing, OCR, validation engine |
5. Identified **Gold Source** (`MORE ZIP FILES-05-19-25/app`) as the primary base for UI components.
6. Discovered advanced functionality in root `app` directory (document processing, OCR, validation engine, reviews, reporting).

## Phase 1 – Consolidation & Core UI (1-2 weeks)
1. **Setup Base Application**
   - Copy Gold Source to `/app` at repo root if not already done
   - Ensure proper configuration:
     - Single `tailwind.config.{js,ts}`
     - Single `next.config.{js,ts}`
     - Single `tsconfig.json`
   - Standardize folder layout:
     ```
     /app            – Next.js app router files
     /prisma         – schema & migrations
     /public         – static assets
     /lib            – shared helpers (auth, prisma, utils)
     /components     – shared React components
     /hooks          – shared React hooks
     /types          – TypeScript type definitions
     ```

2. **Consolidate UI Components**
   - Copy key files from Gold Source:
     - `app/compliance-wizard/page.tsx`
     - `components/compliance-progress-tracker.tsx`
     - `lib/data.ts`, `lib/utils.ts`, `lib/types.ts`
   - Integrate UI components from Express app if missing from Gold Source
   - Ensure UI consistency with Abacus screenshots

3. **Implement Advanced Feature Components**
   - Migrate Document Processing components:
     - `lib/documentProcessor.ts`
     - Document upload/preview UI
   - Migrate Validation Engine components:
     - `lib/complianceChecker.ts`
     - `lib/validationRules.ts`
   - Migrate QA Engine components:
     - `components/qa-engine/*.tsx`

4. **Complete Core UI Pages**
   - Dashboard: Charts and metrics
   - Projects: List and detail views
   - Documents: Upload, list, and preview
   - Settings: User preferences form
   - Wizard: Complete multi-step flow

5. **Implement Client-Side Validation**
   - Add Zod schemas for all forms
   - Implement error messages and visual feedback

## Phase 2 – Data Layer & Authentication (2-3 weeks)
1. **Database Schema Enhancement**
   - Expand `prisma/schema.prisma` with:
     - Users (with roles: admin, reviewer, applicant)
     - Projects (with stages and compliance status)
     - Documents (with metadata: type, OCR text, tags)
     - Compliance requirements (rules, thresholds)
     - Audit logs (actions, timestamps, users)
   - Run migrations and seed with test data

2. **Authentication System**
   - Implement NextAuth.js with:
     - Login/register pages
     - Role-based access control
     - Session management
     - Password reset flow

3. **API Routes Development**
   - Create Next.js API endpoints for:
     - Projects: CRUD operations
     - Documents: Upload, fetch, tag
     - Compliance: Validation, results logging
     - Users: Profile management
   - Add request validation and error handling

4. **Server Actions Integration**
   - Implement form submissions using Server Actions
   - Add optimistic updates for better UX
   - Implement proper error handling

## Phase 3 – Advanced Feature Integration (3-4 weeks)
1. **Document Management System**
   - Integrate OCR (Tesseract.js):
     - PDF text extraction
     - Image processing
     - Document type recognition
   - Implement document storage:
     - File uploads to filesystem or S3
     - Metadata storage in database
     - Document versioning

2. **Compliance Validation Engine**
   - Enhance validation rules:
     - Cost reasonableness checks
     - EHP (Environmental and Historic Preservation) validation
     - Mitigation requirements validation
   - Add AI-powered validation:
     - OpenAI integration for compliance checking
     - Confidence scoring system
     - Policy matching and recommendations

3. **Review Workflows**
   - Implement multi-stage review process:
     - Initial submission
     - Technical review
     - Final approval
   - Add status tracking and notifications

4. **Reporting System**
   - Generate compliance reports:
     - PDF generation with compliance summaries
     - Cost analysis reports
     - Status reports for projects
   - Implement audit trails

## Phase 4 – Testing & Optimization (1-2 weeks)
1. **Comprehensive Testing**
   - Unit tests:
     - Validation rules
     - Utility functions
     - UI components
   - Integration tests:
     - API endpoints
     - Database operations
   - End-to-end tests:
     - User flows (wizard, document upload, etc.)

2. **Performance Optimization**
   - Optimize database queries:
     - Prisma query optimization
     - Eager loading related data
   - Implement caching:
     - Redis for frequent data
     - Report caching
   - Improve UI performance:
     - Optimize component rendering
     - Implement virtualization for large lists

3. **Security Hardening**
   - Implement proper CSRF protection
   - Add rate limiting for API routes
   - Audit authentication flows
   - Validate file uploads for security

## Phase 5 – Deployment & Documentation (1 week)
1. **Deployment Configuration**
   - Production build optimization
   - Environment variable documentation
   - Dockerfile or Vercel configuration

2. **Documentation**
   - User manual
   - Admin guide
   - API documentation
   - Deployment guide

3. **CI/CD Setup**
   - GitHub Actions for:
     - Linting
     - Testing
     - Building
     - Deployment

---

## Change Log (chronological)
| Date (UTC) | Author | Description |
| ---------- | ------ | ----------- |
| 2025-05-20 | AI-assistant | Initial roadmap & structure created. |
| 2025-05-20 | AI-assistant | Fixed build: added chart.js + react-chartjs-2, pinned framer-motion→11.10.0; build succeeds; marked Gold Source |
| 2025-05-22 | AI-assistant | Updated roadmap with comprehensive plan incorporating advanced features discovered in codebase analysis |
| 2025-05-22 | AI-assistant | Implemented core compliance wizard and components from Gold Source into clean-app. Created README and CHANGELOG |

> _Add new rows underneath for every commit / significant action._ 

> *Static-analysis results — will confirm via live run next step.* 