"use strict";function r(){for(var r,o,t=0,n="";t<arguments.length;)(r=arguments[t++])&&(o=e(r))&&(n&&(n+=" "),n+=o);return n}function e(r){if("string"==typeof r)return r;for(var o,t="",n=0;n<r.length;n++)r[n]&&(o=e(r[n]))&&(t&&(t+=" "),t+=o);return t}Object.defineProperty(exports,"__esModule",{value:!0});var o="-";function t(r){var e=function(r){var e=r.theme,o=r.prefix,t={nextPart:new Map,validators:[]},n=function(r,e){return e?r.map((function(r){return[r[0],r[1].map((function(r){return"string"==typeof r?e+r:"object"==typeof r?Object.fromEntries(Object.entries(r).map((function(r){return[e+r[0],r[1]]}))):r}))]})):r}(Object.entries(r.classGroups),o);return n.forEach((function(r){a(r[1],t,r[0],e)})),t}(r),t=r.conflictingClassGroups,l=r.conflictingClassGroupModifiers,s=void 0===l?{}:l;return{getClassGroupId:function(r){var t=r.split(o);return""===t[0]&&1!==t.length&&t.shift(),n(t,e)||function(r){if(i.test(r)){var e=i.exec(r)[1],o=e?.substring(0,e.indexOf(":"));if(o)return"arbitrary.."+o}}(r)},getConflictingClassGroupIds:function(r,e){var o=t[r]||[];return e&&s[r]?[].concat(o,s[r]):o}}}function n(r,e){if(0===r.length)return e.classGroupId;var t=e.nextPart.get(r[0]),i=t?n(r.slice(1),t):void 0;if(i)return i;if(0!==e.validators.length){var a=r.join(o);return e.validators.find((function(r){return(0,r.validator)(a)}))?.classGroupId}}var i=/^\[(.+)\]$/;function a(r,e,o,t){r.forEach((function(r){if("string"!=typeof r){if("function"==typeof r)return r.isThemeGetter?void a(r(t),e,o,t):void e.validators.push({validator:r,classGroupId:o});Object.entries(r).forEach((function(r){a(r[1],l(e,r[0]),o,t)}))}else(""===r?e:l(e,r)).classGroupId=o}))}function l(r,e){var t=r;return e.split(o).forEach((function(r){t.nextPart.has(r)||t.nextPart.set(r,{nextPart:new Map,validators:[]}),t=t.nextPart.get(r)})),t}function s(r){if(r<1)return{get:function(){},set:function(){}};var e=0,o=new Map,t=new Map;function n(n,i){o.set(n,i),++e>r&&(e=0,t=o,o=new Map)}return{get:function(r){var e=o.get(r);return void 0!==e?e:void 0!==(e=t.get(r))?(n(r,e),e):void 0},set:function(r,e){o.has(r)?o.set(r,e):n(r,e)}}}var c="!";function d(r){var e=r.separator||":",o=1===e.length,t=e[0],n=e.length;return function(r){for(var i,a=[],l=0,s=0,d=0;d<r.length;d++){var u=r[d];if(0===l){if(u===t&&(o||r.slice(d,d+n)===e)){a.push(r.slice(s,d)),s=d+n;continue}if("/"===u){i=d;continue}}"["===u?l++:"]"===u&&l--}var p=0===a.length?r:r.substring(s),f=p.startsWith(c);return{modifiers:a,hasImportantModifier:f,baseClassName:f?p.substring(1):p,maybePostfixModifierPosition:i&&i>s?i-s:void 0}}}var u=/\s+/;function p(){for(var e=arguments.length,o=new Array(e),n=0;n<e;n++)o[n]=arguments[n];var i,a,l,p=function(r){var e=o[0],n=o.slice(1).reduce((function(r,e){return e(r)}),e());return i=function(r){return{cache:s(r.cacheSize),splitModifiers:d(r),...t(r)}}(n),a=i.cache.get,l=i.cache.set,p=f,f(r)};function f(r){var e=a(r);if(e)return e;var o=function(r,e){var o=e.splitModifiers,t=e.getClassGroupId,n=e.getConflictingClassGroupIds,i=new Set;return r.trim().split(u).map((function(r){var e=o(r),n=e.modifiers,i=e.hasImportantModifier,a=e.baseClassName,l=e.maybePostfixModifierPosition,s=t(l?a.substring(0,l):a),d=Boolean(l);if(!s){if(!l)return{isTailwindClass:!1,originalClassName:r};if(!(s=t(a)))return{isTailwindClass:!1,originalClassName:r};d=!1}var u=function(r){if(r.length<=1)return r;var e=[],o=[];return r.forEach((function(r){"["===r[0]?(e.push.apply(e,o.sort().concat([r])),o=[]):o.push(r)})),e.push.apply(e,o.sort()),e}(n).join(":");return{isTailwindClass:!0,modifierId:i?u+c:u,classGroupId:s,originalClassName:r,hasPostfixModifier:d}})).reverse().filter((function(r){if(!r.isTailwindClass)return!0;var e=r.modifierId,o=r.classGroupId,t=r.hasPostfixModifier,a=e+o;return!i.has(a)&&(i.add(a),n(o,t).forEach((function(r){return i.add(e+r)})),!0)})).reverse().map((function(r){return r.originalClassName})).join(" ")}(r,i);return l(r,o),o}return function(){return p(r.apply(null,arguments))}}function f(r){var e=function(e){return e[r]||[]};return e.isThemeGetter=!0,e}var b=/^\[(?:([a-z-]+):)?(.+)\]$/i,m=/^\d+\/\d+$/,g=new Set(["px","full","screen"]),h=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,v=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function x(r){return M(r)||g.has(r)||m.test(r)||w(r)}function w(r){return T(r,"length",O)}function k(r){return T(r,"size",E)}function z(r){return T(r,"position",E)}function C(r){return T(r,"url",_)}function j(r){return T(r,"number",M)}function M(r){return!Number.isNaN(Number(r))}function G(r){return r.endsWith("%")&&M(r.slice(0,-1))}function P(r){return W(r)||T(r,"number",W)}function I(r){return b.test(r)}function A(){return!0}function N(r){return h.test(r)}function S(r){return T(r,"",$)}function T(r,e,o){var t=b.exec(r);return!!t&&(t[1]?t[1]===e:o(t[2]))}function O(r){return v.test(r)}function E(){return!1}function _(r){return r.startsWith("url(")}function W(r){return Number.isInteger(Number(r))}function $(r){return y.test(r)}var R={__proto__:null,isAny:A,isArbitraryLength:w,isArbitraryNumber:j,isArbitraryPosition:z,isArbitraryShadow:S,isArbitrarySize:k,isArbitraryUrl:C,isArbitraryValue:I,isArbitraryWeight:j,isInteger:P,isLength:x,isNumber:M,isPercent:G,isTshirtSize:N};function q(){var r=f("colors"),e=f("spacing"),o=f("blur"),t=f("brightness"),n=f("borderColor"),i=f("borderRadius"),a=f("borderSpacing"),l=f("borderWidth"),s=f("contrast"),c=f("grayscale"),d=f("hueRotate"),u=f("invert"),p=f("gap"),b=f("gradientColorStops"),m=f("gradientColorStopPositions"),g=f("inset"),h=f("margin"),v=f("opacity"),y=f("padding"),T=f("saturate"),O=f("scale"),E=f("sepia"),_=f("skew"),W=f("space"),$=f("translate"),R=function(){return["auto",I,e]},q=function(){return[I,e]},L=function(){return["",x]},B=function(){return["auto",M,I]},D=function(){return["","0",I]},J=function(){return[M,j]},U=function(){return[M,I]};return{cacheSize:500,theme:{colors:[A],spacing:[x],blur:["none","",N,I],brightness:J(),borderColor:[r],borderRadius:["none","","full",N,I],borderSpacing:q(),borderWidth:L(),contrast:J(),grayscale:D(),hueRotate:U(),invert:D(),gap:q(),gradientColorStops:[r],gradientColorStopPositions:[G,w],inset:R(),margin:R(),opacity:J(),padding:q(),saturate:J(),scale:J(),sepia:D(),skew:U(),space:q(),translate:q()},classGroups:{aspect:[{aspect:["auto","square","video",I]}],container:["container"],columns:[{columns:[N]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],[I])}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",P]}],basis:[{basis:R()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",I]}],grow:[{grow:D()}],shrink:[{shrink:D()}],order:[{order:["first","last","none",P]}],"grid-cols":[{"grid-cols":[A]}],"col-start-end":[{col:["auto",{span:["full",P]},I]}],"col-start":[{"col-start":B()}],"col-end":[{"col-end":B()}],"grid-rows":[{"grid-rows":[A]}],"row-start-end":[{row:["auto",{span:[P]},I]}],"row-start":[{"row-start":B()}],"row-end":[{"row-end":B()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",I]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",I]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal"].concat(["start","end","center","between","around","evenly","stretch"])}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(["start","end","center","between","around","evenly","stretch"],["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(["start","end","center","between","around","evenly","stretch"],["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[W]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[W]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",I,e]}],"min-w":[{"min-w":["min","max","fit",I,x]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[N]},N,I]}],h:[{h:[I,e,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",I,x]}],"max-h":[{"max-h":[I,e,"min","max","fit"]}],"font-size":[{text:["base",N,w]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",j]}],"font-family":[{font:[A]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",I]}],"line-clamp":[{"line-clamp":["none",M,j]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,x]}],"list-image":[{"list-image":["none",I]}],"list-style-type":[{list:["none","disc","decimal",I]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[r]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[r]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(["solid","dashed","dotted","double","none"],["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",x]}],"underline-offset":[{"underline-offset":["auto",I,x]}],"text-decoration-color":[{decoration:[r]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",I]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",I]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],[z])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",k]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},C]}],"bg-color":[{bg:[r]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[b]}],"gradient-via":[{via:[b]}],"gradient-to":[{to:[b]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[].concat(["solid","dashed","dotted","double","none"],["hidden"])}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:[""].concat(["solid","dashed","dotted","double","none"])}],"outline-offset":[{"outline-offset":[I,x]}],"outline-w":[{outline:[x]}],"outline-color":[{outline:[r]}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[r]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[x]}],"ring-offset-color":[{"ring-offset":[r]}],shadow:[{shadow:["","inner","none",N,S]}],"shadow-color":[{shadow:[A]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]}],filter:[{filter:["","none"]}],blur:[{blur:[o]}],brightness:[{brightness:[t]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",N,I]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[T]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[o]}],"backdrop-brightness":[{"backdrop-brightness":[t]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[T]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",I]}],duration:[{duration:U()}],ease:[{ease:["linear","in","out","in-out",I]}],delay:[{delay:U()}],animate:[{animate:["none","spin","ping","pulse","bounce",I]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[O]}],"scale-x":[{"scale-x":[O]}],"scale-y":[{"scale-y":[O]}],rotate:[{rotate:[P,I]}],"translate-x":[{"translate-x":[$]}],"translate-y":[{"translate-y":[$]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",I]}],accent:[{accent:["auto",r]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",I]}],"caret-color":[{caret:[r]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",I]}],fill:[{fill:[r,"none"]}],"stroke-w":[{stroke:[x,j]}],stroke:[{stroke:[r,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}function L(r,e){for(var o in e)J(r,o,e[o]);return r}var B=Object.prototype.hasOwnProperty,D=new Set(["string","number","boolean"]);function J(r,e,o){if(B.call(r,e)&&!D.has(typeof o)&&null!==o){if(Array.isArray(o)&&Array.isArray(r[e]))r[e]=r[e].concat(o);else if("object"==typeof o&&"object"==typeof r[e]){if(null===r[e])return void(r[e]=o);for(var t in o)J(r[e],t,o[t])}}else r[e]=o}var U=p(q),V=r;exports.createTailwindMerge=p,exports.extendTailwindMerge=function(r){for(var e=arguments.length,o=new Array(e>1?e-1:0),t=1;t<e;t++)o[t-1]=arguments[t];return p.apply(void 0,"function"==typeof r?[q,r].concat(o):[function(){return L(q(),r)}].concat(o))},exports.fromTheme=f,exports.getDefaultConfig=q,exports.join=V,exports.mergeConfigs=L,exports.twJoin=r,exports.twMerge=U,exports.validators=R;
//# sourceMappingURL=tailwind-merge.cjs.production.min.js.map
