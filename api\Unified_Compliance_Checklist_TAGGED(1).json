{"Phase 1: Declaration and Initial Eligibility::GROK.General": [{"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Presidential Disaster Declaration", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "FEMA Issues Declaration and Designates Areas", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Incident Period and Deadlines", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Assess Applicant Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Facility Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Presidential Disaster Declaration", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "FEMA Issues Declaration and Designates Areas", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Incident Period and Deadlines", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Assess Applicant Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Facility Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Presidential Disaster Declaration", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "FEMA Issues Declaration and Designates Areas", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Incident Period and Deadlines", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Assess Applicant Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Facility Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Presidential Disaster Declaration", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "FEMA Issues Declaration and Designates Areas", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Incident Period and Deadlines", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Assess Applicant Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Facility Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Presidential Disaster Declaration", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "FEMA Issues Declaration and Designates Areas", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Incident Period and Deadlines", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Assess Applicant Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Facility Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Presidential Disaster Declaration", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "FEMA Issues Declaration and Designates Areas", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Incident Period and Deadlines", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Assess Applicant Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Facility Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declaration and Initial Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 1: Declarations and Planning::GROK.General": [{"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Request Submission", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Evaluation by FEMA", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Presidential Declaration Determination", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Request Submission", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Evaluation by FEMA", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Presidential Declaration Determination", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Request Submission", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Evaluation by FEMA", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Presidential Declaration Determination", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Request Submission", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Evaluation by FEMA", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Presidential Declaration Determination", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Request Submission", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Declaration Evaluation by FEMA", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 1: Declarations and Planning", "step/requirement": "Presidential Declaration Determination", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 2: Applicant Eligibility and RPA::GROK.General": [{"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Applicant Briefing", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "FEMA Review of Applicant Eligibility", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Facility Eligibility Determination", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Legal Responsibility Validation", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Applicant Briefing", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "FEMA Review of Applicant Eligibility", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Facility Eligibility Determination", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Legal Responsibility Validation", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Applicant Briefing", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "FEMA Review of Applicant Eligibility", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Facility Eligibility Determination", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Legal Responsibility Validation", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Applicant Briefing", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "FEMA Review of Applicant Eligibility", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Facility Eligibility Determination", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: Applicant Eligibility and RPA", "step/requirement": "Legal Responsibility Validation", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 2: RPA and Applicant Briefing::GROK.General": [{"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Attend Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Assign Program Delivery Manager (PDMG)", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Damage Inventory (DI)", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Attend Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Assign Program Delivery Manager (PDMG)", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Damage Inventory (DI)", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Attend Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Assign Program Delivery Manager (PDMG)", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Damage Inventory (DI)", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Attend Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Assign Program Delivery Manager (PDMG)", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Damage Inventory (DI)", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Attend Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Request for Public Assistance (RPA)", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Assign Program Delivery Manager (PDMG)", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 2: RPA and Applicant Briefing", "step/requirement": "Submit Damage Inventory (DI)", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 3: Damage & Facility Eligibility::GROK.General": [{"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Document Incident-Related Damages", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Confirm Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Document Incident-Related Damages", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Confirm Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Document Incident-Related Damages", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Confirm Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Document Incident-Related Damages", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Confirm Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Work Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage & Facility Eligibility", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 3: Damage and Facility/Work Eligibility::GROK.General": [{"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Damage must result from declared incident", "trigger_condition_if": "Facility shows damage", "action_required_then": "Demonstrate damage is directly caused by the declared event", "documentation_required": "Photos, inspection reports, time-stamped logs, incident narratives", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4 & 5, Table 7", "notes": "Damage not caused by the incident is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Facility must be within designated disaster area", "trigger_condition_if": "Applicant submits project", "action_required_then": "Verify physical location is within the federally declared disaster area", "documentation_required": "Maps, GPS data, address verification, damage site photos", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 8", "notes": "Essential to prove eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Establish Legal Responsibility", "trigger_condition_if": "Work is claimed on damaged facility or site", "action_required_then": "Show proof that applicant had legal responsibility at time of disaster", "documentation_required": "Ownership documents, leases, contracts, jurisdictional maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 9", "notes": "Work done without legal authority is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Emergency Work vs. Permanent Work Classification", "trigger_condition_if": "Work is being scoped for eligibility", "action_required_then": "Classify as Emergency (Cat A/B) or Permanent (Cat C-G) work", "documentation_required": "Work orders, activity logs, incident response documents", "responsible_party": "Applicant, reviewed by FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4 Section II", "notes": "Incorrect classification can delay funding", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Submit Impact List and Grouping", "trigger_condition_if": "Applicant has multiple damaged sites or assets", "action_required_then": "Group damages into projects and submit impact list", "documentation_required": "Impact list template, supporting estimates, photos, maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 5", "notes": "Proper grouping streamlines project formulation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Damage must result from declared incident", "trigger_condition_if": "Facility shows damage", "action_required_then": "Demonstrate damage is directly caused by the declared event", "documentation_required": "Photos, inspection reports, time-stamped logs, incident narratives", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4 & 5, Table 7", "notes": "Damage not caused by the incident is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Facility must be within designated disaster area", "trigger_condition_if": "Applicant submits project", "action_required_then": "Verify physical location is within the federally declared disaster area", "documentation_required": "Maps, GPS data, address verification, damage site photos", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 8", "notes": "Essential to prove eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Establish Legal Responsibility", "trigger_condition_if": "Work is claimed on damaged facility or site", "action_required_then": "Show proof that applicant had legal responsibility at time of disaster", "documentation_required": "Ownership documents, leases, contracts, jurisdictional maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 9", "notes": "Work done without legal authority is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Emergency Work vs. Permanent Work Classification", "trigger_condition_if": "Work is being scoped for eligibility", "action_required_then": "Classify as Emergency (Cat A/B) or Permanent (Cat C-G) work", "documentation_required": "Work orders, activity logs, incident response documents", "responsible_party": "Applicant, reviewed by FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4 Section II", "notes": "Incorrect classification can delay funding", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Submit Impact List and Grouping", "trigger_condition_if": "Applicant has multiple damaged sites or assets", "action_required_then": "Group damages into projects and submit impact list", "documentation_required": "Impact list template, supporting estimates, photos, maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 5", "notes": "Proper grouping streamlines project formulation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Damage must result from declared incident", "trigger_condition_if": "Facility shows damage", "action_required_then": "Demonstrate damage is directly caused by the declared event", "documentation_required": "Photos, inspection reports, time-stamped logs, incident narratives", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4 & 5, Table 7", "notes": "Damage not caused by the incident is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Facility must be within designated disaster area", "trigger_condition_if": "Applicant submits project", "action_required_then": "Verify physical location is within the federally declared disaster area", "documentation_required": "Maps, GPS data, address verification, damage site photos", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 8", "notes": "Essential to prove eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Establish Legal Responsibility", "trigger_condition_if": "Work is claimed on damaged facility or site", "action_required_then": "Show proof that applicant had legal responsibility at time of disaster", "documentation_required": "Ownership documents, leases, contracts, jurisdictional maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 9", "notes": "Work done without legal authority is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Emergency Work vs. Permanent Work Classification", "trigger_condition_if": "Work is being scoped for eligibility", "action_required_then": "Classify as Emergency (Cat A/B) or Permanent (Cat C-G) work", "documentation_required": "Work orders, activity logs, incident response documents", "responsible_party": "Applicant, reviewed by FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4 Section II", "notes": "Incorrect classification can delay funding", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 3: Damage and Facility/Work Eligibility", "step/requirement": "Submit Impact List and Grouping", "trigger_condition_if": "Applicant has multiple damaged sites or assets", "action_required_then": "Group damages into projects and submit impact list", "documentation_required": "Impact list template, supporting estimates, photos, maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 5", "notes": "Proper grouping streamlines project formulation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 4: Cost Eligibility & Procurement::GROK.General": [{"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Identify All Cost Types (Force Account, Contract, Materials)", "trigger_condition_if": "Work has been completed or is ongoing", "action_required_then": "Categorize expenses: labor, equipment, materials, contracts, donations, volunteers", "documentation_required": "Timesheets, equipment logs, invoices, procurement records", "responsible_party": "Applicant", "notes": "Each cost must be directly tied to eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II–IV", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Document Force Account Labor (FAL)", "trigger_condition_if": "Applicant used their own employees for response or repairs", "action_required_then": "Track hours, activities, pay rates for each employee by project", "documentation_required": "Timesheets, payroll records, labor policies, fringe benefit breakdowns", "responsible_party": "Applicant", "notes": "Track emergency vs. permanent work separately", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Document Force Account Equipment (FAE)", "trigger_condition_if": "Applicant used owned or leased equipment", "action_required_then": "Log hours, type, operator, and task for each equipment use", "documentation_required": "Daily usage logs, FEMA equipment rate schedules, operator assignments", "responsible_party": "Applicant", "notes": "Use FEMA or state-approved equipment rates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Ensure Procurement Compliance", "trigger_condition_if": "Work performed by contracts or vendors", "action_required_then": "Comply with 2 CFR §200 procurement standards", "documentation_required": "Bid documents, RFPs, contracts, scoring sheets, justifications", "responsible_party": "Applicant", "notes": "Use competitive procurement unless exceptions apply", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § VI; 2 CFR §200.317–327", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Track Material and Supply Costs", "trigger_condition_if": "Materials were purchased or used from inventory", "action_required_then": "Provide quantity, unit price, source, and use case", "documentation_required": "Receipts, inventory logs, invoices, delivery tickets", "responsible_party": "Applicant", "notes": "Only materials used for eligible work are reimbursable", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.3", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Account for Mutual Aid and Volunteer Labor", "trigger_condition_if": "External public or volunteer resources were used", "action_required_then": "Document hours, type of service, agreements or offers of assistance", "documentation_required": "Mutual aid agreements, volunteer sign-in sheets, service logs", "responsible_party": "Applicant", "notes": "Labor must be tracked and assigned per project", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.C", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Verify Cost Reasonableness", "trigger_condition_if": "All cost data is collected", "action_required_then": "Assess cost reasonableness compared to market rates", "documentation_required": "Comparative cost analyses, FEMA unit cost guides, vendor quotes", "responsible_party": "FEMA and Applicant", "notes": "Unreasonable costs are subject to reduction", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II.E", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Identify All Cost Types (Force Account, Contract, Materials)", "trigger_condition_if": "Work has been completed or is ongoing", "action_required_then": "Categorize expenses: labor, equipment, materials, contracts, donations, volunteers", "documentation_required": "Timesheets, equipment logs, invoices, procurement records", "responsible_party": "Applicant", "notes": "Each cost must be directly tied to eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II–IV", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Document Force Account Labor (FAL)", "trigger_condition_if": "Applicant used their own employees for response or repairs", "action_required_then": "Track hours, activities, pay rates for each employee by project", "documentation_required": "Timesheets, payroll records, labor policies, fringe benefit breakdowns", "responsible_party": "Applicant", "notes": "Track emergency vs. permanent work separately", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Document Force Account Equipment (FAE)", "trigger_condition_if": "Applicant used owned or leased equipment", "action_required_then": "Log hours, type, operator, and task for each equipment use", "documentation_required": "Daily usage logs, FEMA equipment rate schedules, operator assignments", "responsible_party": "Applicant", "notes": "Use FEMA or state-approved equipment rates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Ensure Procurement Compliance", "trigger_condition_if": "Work performed by contracts or vendors", "action_required_then": "Comply with 2 CFR §200 procurement standards", "documentation_required": "Bid documents, RFPs, contracts, scoring sheets, justifications", "responsible_party": "Applicant", "notes": "Use competitive procurement unless exceptions apply", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § VI; 2 CFR §200.317–327", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Track Material and Supply Costs", "trigger_condition_if": "Materials were purchased or used from inventory", "action_required_then": "Provide quantity, unit price, source, and use case", "documentation_required": "Receipts, inventory logs, invoices, delivery tickets", "responsible_party": "Applicant", "notes": "Only materials used for eligible work are reimbursable", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.3", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Account for Mutual Aid and Volunteer Labor", "trigger_condition_if": "External public or volunteer resources were used", "action_required_then": "Document hours, type of service, agreements or offers of assistance", "documentation_required": "Mutual aid agreements, volunteer sign-in sheets, service logs", "responsible_party": "Applicant", "notes": "Labor must be tracked and assigned per project", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.C", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Verify Cost Reasonableness", "trigger_condition_if": "All cost data is collected", "action_required_then": "Assess cost reasonableness compared to market rates", "documentation_required": "Comparative cost analyses, FEMA unit cost guides, vendor quotes", "responsible_party": "FEMA and Applicant", "notes": "Unreasonable costs are subject to reduction", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II.E", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Identify All Cost Types (Force Account, Contract, Materials)", "trigger_condition_if": "Work has been completed or is ongoing", "action_required_then": "Categorize expenses: labor, equipment, materials, contracts, donations, volunteers", "documentation_required": "Timesheets, equipment logs, invoices, procurement records", "responsible_party": "Applicant", "notes": "Each cost must be directly tied to eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II–IV", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Document Force Account Labor (FAL)", "trigger_condition_if": "Applicant used their own employees for response or repairs", "action_required_then": "Track hours, activities, pay rates for each employee by project", "documentation_required": "Timesheets, payroll records, labor policies, fringe benefit breakdowns", "responsible_party": "Applicant", "notes": "Track emergency vs. permanent work separately", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Document Force Account Equipment (FAE)", "trigger_condition_if": "Applicant used owned or leased equipment", "action_required_then": "Log hours, type, operator, and task for each equipment use", "documentation_required": "Daily usage logs, FEMA equipment rate schedules, operator assignments", "responsible_party": "Applicant", "notes": "Use FEMA or state-approved equipment rates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.2", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Ensure Procurement Compliance", "trigger_condition_if": "Work performed by contracts or vendors", "action_required_then": "Comply with 2 CFR §200 procurement standards", "documentation_required": "Bid documents, RFPs, contracts, scoring sheets, justifications", "responsible_party": "Applicant", "notes": "Use competitive procurement unless exceptions apply", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § VI; 2 CFR §200.317–327", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Track Material and Supply Costs", "trigger_condition_if": "Materials were purchased or used from inventory", "action_required_then": "Provide quantity, unit price, source, and use case", "documentation_required": "Receipts, inventory logs, invoices, delivery tickets", "responsible_party": "Applicant", "notes": "Only materials used for eligible work are reimbursable", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.3", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Account for Mutual Aid and Volunteer Labor", "trigger_condition_if": "External public or volunteer resources were used", "action_required_then": "Document hours, type of service, agreements or offers of assistance", "documentation_required": "Mutual aid agreements, volunteer sign-in sheets, service logs", "responsible_party": "Applicant", "notes": "Labor must be tracked and assigned per project", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.C", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility & Procurement", "step/requirement": "Verify Cost Reasonableness", "trigger_condition_if": "All cost data is collected", "action_required_then": "Assess cost reasonableness compared to market rates", "documentation_required": "Comparative cost analyses, FEMA unit cost guides, vendor quotes", "responsible_party": "FEMA and Applicant", "notes": "Unreasonable costs are subject to reduction", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II.E", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 4: Cost Eligibility and Procurement::GROK.General": [{"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Establish Reasonable Cost", "trigger_condition_if": "Applicant submits project costs", "action_required_then": "Demonstrate costs are necessary and reasonable for the work performed", "documentation_required": "Cost comparisons, historical pricing, procurement records, quotes", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § II.A", "notes": "FEMA may require a reasonableness analysis", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Applicant Labor Eligibility", "trigger_condition_if": "Labor costs are claimed", "action_required_then": "Verify labor was disaster-related and within policy rules", "documentation_required": "Timesheets, pay policies, job descriptions, disaster assignments", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, Table 10 & 11", "notes": "Differentiation required for emergency vs permanent work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Procurement Method Compliance", "trigger_condition_if": "Contracted work is used", "action_required_then": "Follow federal procurement rules based on entity type", "documentation_required": "RFPs, bids, contracts, evaluation scoresheets, cost reasonableness", "responsible_party": "Applicant", "applicable_regulations": "2 C.F.R. §§ 200.318–327; PAPPG v5.0, Chapter 6, § X", "notes": "Include time-and-material justification if used", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Mutual Aid Agreements", "trigger_condition_if": "Applicant uses support from another jurisdiction", "action_required_then": "Document mutual aid agreements and actual costs incurred", "documentation_required": "MOUs/MOAs, deployment logs, invoices, cost summaries", "responsible_party": "Applicant and partner entity", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XI", "notes": "Agreements can be pre- or post-incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Validate Donated Resources", "trigger_condition_if": "Volunteer labor or materials are used", "action_required_then": "Track and document donated services for offset credit", "documentation_required": "Timesheets, material logs, fair market valuation", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XVI, Table 18", "notes": "Must directly support eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Establish Reasonable Cost", "trigger_condition_if": "Applicant submits project costs", "action_required_then": "Demonstrate costs are necessary and reasonable for the work performed", "documentation_required": "Cost comparisons, historical pricing, procurement records, quotes", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § II.A", "notes": "FEMA may require a reasonableness analysis", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Applicant Labor Eligibility", "trigger_condition_if": "Labor costs are claimed", "action_required_then": "Verify labor was disaster-related and within policy rules", "documentation_required": "Timesheets, pay policies, job descriptions, disaster assignments", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, Table 10 & 11", "notes": "Differentiation required for emergency vs permanent work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Procurement Method Compliance", "trigger_condition_if": "Contracted work is used", "action_required_then": "Follow federal procurement rules based on entity type", "documentation_required": "RFPs, bids, contracts, evaluation scoresheets, cost reasonableness", "responsible_party": "Applicant", "applicable_regulations": "2 C.F.R. §§ 200.318–327; PAPPG v5.0, Chapter 6, § X", "notes": "Include time-and-material justification if used", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Mutual Aid Agreements", "trigger_condition_if": "Applicant uses support from another jurisdiction", "action_required_then": "Document mutual aid agreements and actual costs incurred", "documentation_required": "MOUs/MOAs, deployment logs, invoices, cost summaries", "responsible_party": "Applicant and partner entity", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XI", "notes": "Agreements can be pre- or post-incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 4: Cost Eligibility and Procurement", "step/requirement": "Validate Donated Resources", "trigger_condition_if": "Volunteer labor or materials are used", "action_required_then": "Track and document donated services for offset credit", "documentation_required": "Timesheets, material logs, fair market valuation", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XVI, Table 18", "notes": "Must directly support eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 5: Project Formulation & Scope::GROK.General": [{"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Initiate Project Formulation", "trigger_condition_if": "Damage Inventory and cost documentation are complete", "action_required_then": "Begin formulation of individual Project Worksheets (PWs)", "documentation_required": "DI, RSM notes, cost documentation, site photos, insurance info", "responsible_party": "Applicant and PDMG", "notes": "Projects may be grouped or separated by type, site, or funding stream", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § I–II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Define Scope of Work (SOW)", "trigger_condition_if": "Project formulation is underway", "action_required_then": "Describe eligible work in detail, including methods, materials, timeline", "documentation_required": "Engineer estimates, photos, site maps, damage descriptions", "responsible_party": "Applicant, reviewed by FEMA", "notes": "SOW must match eligible damages and clearly exclude ineligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Apply Consensus-Based Codes and Standards (CBCS)", "trigger_condition_if": "Permanent work involves repair or replacement", "action_required_then": "Ensure design follows latest published and appropriate CBCS", "documentation_required": "Building codes, engineering specs, code citations, local ordinances", "responsible_party": "Applicant and design professionals", "notes": "Must be uniformly enforced and apply to all similar facilities", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 7 § II.A.1; Stafford Act §406(e)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Perform Environmental and Historic Preservation (EHP) Review", "trigger_condition_if": "Project involves ground disturbance, historic properties, or protected areas", "action_required_then": "Complete FEMA EHP review before obligation of funds", "documentation_required": "Maps, permits, consultation letters (SHPO, USFWS, EPA), NEPA documentation", "responsible_party": "FEMA EHP with Applicant support", "notes": "Must identify all environmental and cultural impacts", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA, NHPA, ESA, 44 CFR Part 9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Identify Section 406 Hazard Mitigation Proposals", "trigger_condition_if": "Permanent work allows opportunity to reduce future risk", "action_required_then": "Integrate mitigation measures into SOW and estimate", "documentation_required": "Risk analysis, benefit-cost data, engineering justifications", "responsible_party": "Applicant and PDMG", "notes": "Mitigation must be cost-effective and technically feasible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406(c)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Develop Cost Estimate", "trigger_condition_if": "SOW is defined", "action_required_then": "Prepare cost estimate using approved methods (e.g., cost codes, RSMeans)", "documentation_required": "Line-item cost breakdowns, quotes, cost guides", "responsible_party": "Applicant, reviewed by FEMA", "notes": "FEMA may validate estimate using their Cost Estimating Format (CEF)", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 6 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Initiate Project Formulation", "trigger_condition_if": "Damage Inventory and cost documentation are complete", "action_required_then": "Begin formulation of individual Project Worksheets (PWs)", "documentation_required": "DI, RSM notes, cost documentation, site photos, insurance info", "responsible_party": "Applicant and PDMG", "notes": "Projects may be grouped or separated by type, site, or funding stream", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § I–II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Define Scope of Work (SOW)", "trigger_condition_if": "Project formulation is underway", "action_required_then": "Describe eligible work in detail, including methods, materials, timeline", "documentation_required": "Engineer estimates, photos, site maps, damage descriptions", "responsible_party": "Applicant, reviewed by FEMA", "notes": "SOW must match eligible damages and clearly exclude ineligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Apply Consensus-Based Codes and Standards (CBCS)", "trigger_condition_if": "Permanent work involves repair or replacement", "action_required_then": "Ensure design follows latest published and appropriate CBCS", "documentation_required": "Building codes, engineering specs, code citations, local ordinances", "responsible_party": "Applicant and design professionals", "notes": "Must be uniformly enforced and apply to all similar facilities", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 7 § II.A.1; Stafford Act §406(e)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Perform Environmental and Historic Preservation (EHP) Review", "trigger_condition_if": "Project involves ground disturbance, historic properties, or protected areas", "action_required_then": "Complete FEMA EHP review before obligation of funds", "documentation_required": "Maps, permits, consultation letters (SHPO, USFWS, EPA), NEPA documentation", "responsible_party": "FEMA EHP with Applicant support", "notes": "Must identify all environmental and cultural impacts", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA, NHPA, ESA, 44 CFR Part 9", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Identify Section 406 Hazard Mitigation Proposals", "trigger_condition_if": "Permanent work allows opportunity to reduce future risk", "action_required_then": "Integrate mitigation measures into SOW and estimate", "documentation_required": "Risk analysis, benefit-cost data, engineering justifications", "responsible_party": "Applicant and PDMG", "notes": "Mitigation must be cost-effective and technically feasible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406(c)", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scope", "step/requirement": "Develop Cost Estimate", "trigger_condition_if": "SOW is defined", "action_required_then": "Prepare cost estimate using approved methods (e.g., cost codes, RSMeans)", "documentation_required": "Line-item cost breakdowns, quotes, cost guides", "responsible_party": "Applicant, reviewed by FEMA", "notes": "FEMA may validate estimate using their Cost Estimating Format (CEF)", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 6 § II", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 5: Project Formulation & Scoping::GROK.General": [{"process_phase": "Phase 5: Project Formulation & Scoping", "step/requirement": "<PERSON><PERSON><PERSON>ope of Work (SOW)", "trigger_condition_if": "Damage is confirmed and costs identified", "action_required_then": "Draft project scope with detailed work description and quantifiable costs", "documentation_required": "Damage descriptions, SOW template, engineering reports, cost data", "responsible_party": "Applicant, with FEMA and Recipient review", "applicable_regulations": "PAPPG v5.0, Chapter 9, § I", "notes": "Scope must reflect eligible work only", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scoping", "step/requirement": "Project Threshold Classification", "trigger_condition_if": "Project cost is estimated", "action_required_then": "Classify as small or large project based on FEMA thresholds", "documentation_required": "Estimated cost worksheets, FEMA thresholds table", "responsible_party": "FEMA and Recipient", "applicable_regulations": "PAPPG v5.0, Chapter 9, § II.A", "notes": "Threshold updated annually", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scoping", "step/requirement": "Conduct Cost Reasonableness Review", "trigger_condition_if": "Costs are submitted with scope", "action_required_then": "FEMA evaluates cost reasonableness for each line item", "documentation_required": "Engineer estimates, historical pricing, procurement docs", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 9, § II.D–E", "notes": "Can use FEMA cost estimating tools", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scoping", "step/requirement": "Environmental & Historic Preservation (EHP) Review", "trigger_condition_if": "Work affects environment or historic structures", "action_required_then": "Submit required documentation for FEMA EHP review", "documentation_required": "Maps, site photos, EHP forms, coordination records", "responsible_party": "Applicant and FEMA EHP staff", "applicable_regulations": "PAPPG v5.0, Chapter 10, § I–II", "notes": "Delays occur without complete EHP data", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 5: Project Formulation & Scoping", "step/requirement": "FEMA Obligation of Funds", "trigger_condition_if": "Project is approved and passes compliance review", "action_required_then": "FEMA obligates funding through Grants Manager", "documentation_required": "Final scope and cost, approval memos, compliance clearance", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 9, § IV", "notes": "Obligation date starts funding and reporting timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}], "Phase 6: Project Review & Obligation::GROK.General": [{"process_phase": "Phase 6: Project Review & Obligation", "step/requirement": "Conduct Compliance Review", "trigger_condition_if": "Project Worksheet is finalized", "action_required_then": "FEMA conducts eligibility, cost, and documentation compliance review", "documentation_required": "Complete PW package, policy citations, supporting documents", "responsible_party": "FEMA and Recipient", "notes": "Projects reviewed for duplication of benefits, cost reasonableness, and eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § III", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 6: Project Review & Obligation", "step/requirement": "Check for Duplication of Benefits (DOB)", "trigger_condition_if": "Insurance, grants, or other funding also apply to damages", "action_required_then": "Ensure PA funding does not duplicate other sources", "documentation_required": "Insurance settlement statements, SBA, HUD, or other grant award letters", "responsible_party": "Applicant and FEMA", "notes": "DOB reductions are mandatory before obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § V.C; Stafford Act §312", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 6: Project Review & Obligation", "step/requirement": "Apply Insurance Reductions", "trigger_condition_if": "Insurable facilities receive insurance payments or are covered by policy", "action_required_then": "Subtract actual or anticipated insurance proceeds from eligible project costs", "documentation_required": "Insurance policies, claims, correspondence with carriers", "responsible_party": "Applicant and FEMA", "notes": "NFIP compliance is mandatory for flood-damaged properties", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § V.C; 44 CFR §206.252–253", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 6: Project Review & Obligation", "step/requirement": "Finalize Project Worksheet (PW)", "trigger_condition_if": "All project review steps are complete", "action_required_then": "Submit PW for final Recipient review and FEMA obligation", "documentation_required": "Finalized PW, approval signatures, version control", "responsible_party": "Applicant, Recipient, and FEMA", "notes": "FEMA issues obligation letter once PW is approved", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § IV", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}, {"process_phase": "Phase 6: Project Review & Obligation", "step/requirement": "Project Obligation Notification", "trigger_condition_if": "FEMA approves and obligates funds", "action_required_then": "Recipient notifies Applicant of approved amount", "documentation_required": "Obligation letter, project summary, fund release notice", "responsible_party": "FEMA and Recipient", "notes": "Starts the clock for project deadlines and closeout", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § IV", "checklist_damageinventory": 0.0, "checklist_damagedescription": 0.0, "checklist_costing": 0.0, "checklist_invoices": 0.0, "checklist_mitigation": 0.0, "checklist_ehp": 0.0, "checklist_insurance": 0.0, "checklist_labordocs": 0.0, "checklist_contracts": 0.0, "checklist_debrisdocs": 0.0, "checklist_progressreports": 0.0, "checklist_closeout": 0.0, "executedcontract_checked": 0.0, "procurementprocedure_checked": 0.0, "solicitation_checked": 0.0, "bid_checked": 0.0, "evaluation_checked": 0.0, "invoice_checked": 0.0, "checkbox": 0.0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "grok_tag": "GROK.General"}]}