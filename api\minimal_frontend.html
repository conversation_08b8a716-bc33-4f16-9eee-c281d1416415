<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax Minimal Frontend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>ComplianceMax Minimal Frontend</h1>
    <p>This is a simplified frontend to test the ComplianceMax application functionality.</p>
    
    <div class="section">
        <h2>Server Status</h2>
        <div>
            <button id="checkBackend">Check Backend (8000)</button>
            <button id="checkMinimalBackend">Check Minimal Backend (9000)</button>
            <button id="checkFrontend">Check Frontend (3000)</button>
        </div>
        <div id="serverStatus"></div>
    </div>
    
    <div class="container">
        <div class="section">
            <h2>Authentication</h2>
            <div>
                <h3>Login</h3>
                <input type="text" id="username" placeholder="Username" value="test_user">
                <input type="password" id="password" placeholder="Password" value="password123">
                <button id="loginBtn">Login</button>
            </div>
            <div>
                <h3>Current User</h3>
                <button id="getCurrentUser">Get Current User</button>
                <div id="userInfo"></div>
            </div>
        </div>
        
        <div class="section">
            <h2>API Endpoints</h2>
            <table>
                <tr>
                    <th>Endpoint</th>
                    <th>Action</th>
                </tr>
                <tr>
                    <td>/api/v1/health</td>
                    <td><button class="testEndpoint" data-endpoint="/api/v1/health">Test</button></td>
                </tr>
                <tr>
                    <td>/api/v1/auth/me</td>
                    <td><button class="testEndpoint" data-endpoint="/api/v1/auth/me">Test</button></td>
                </tr>
                <tr>
                    <td>/api/v1/users</td>
                    <td><button class="testEndpoint" data-endpoint="/api/v1/users">Test</button></td>
                </tr>
                <tr>
                    <td>/api/v1/compliance/applications</td>
                    <td><button class="testEndpoint" data-endpoint="/api/v1/compliance/applications">Test</button></td>
                </tr>
            </table>
            <div id="endpointResult"></div>
        </div>
    </div>
    
    <div class="section">
        <h2>API Response</h2>
        <pre id="apiResponse">No data yet. Click any API endpoint to test.</pre>
    </div>

    <script>
        // Base URLs
        const BACKEND_URL = 'http://localhost:8000';
        const MINIMAL_BACKEND_URL = 'http://localhost:9000';
        const FRONTEND_URL = 'http://localhost:3000';
        
        // Server status checks
        document.getElementById('checkBackend').addEventListener('click', async () => {
            await checkServerStatus(BACKEND_URL, 'Backend');
        });
        
        document.getElementById('checkMinimalBackend').addEventListener('click', async () => {
            await checkServerStatus(MINIMAL_BACKEND_URL, 'Minimal Backend');
        });
        
        document.getElementById('checkFrontend').addEventListener('click', async () => {
            await checkServerStatus(FRONTEND_URL, 'Frontend');
        });
        
        async function checkServerStatus(url, name) {
            const statusElement = document.getElementById('serverStatus');
            statusElement.innerHTML = `Checking ${name}...`;
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${url}/`, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' },
                });
                const endTime = Date.now();
                
                const status = response.status;
                const data = await response.text();
                
                statusElement.innerHTML = `
                    <p><strong>${name}:</strong> <span class="${status < 400 ? 'success' : 'error'}">Status ${status}</span> (${endTime - startTime}ms)</p>
                    <pre>${data}</pre>
                `;
            } catch (error) {
                statusElement.innerHTML = `
                    <p><strong>${name}:</strong> <span class="error">Error connecting</span></p>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        // Login functionality
        document.getElementById('loginBtn').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const userInfoElement = document.getElementById('userInfo');
            const responseElement = document.getElementById('apiResponse');
            
            userInfoElement.innerHTML = 'Logging in...';
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password,
                    }),
                });
                
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
                
                if (response.ok) {
                    userInfoElement.innerHTML = `
                        <p class="success">Login successful!</p>
                        <p>Token: ${data.access_token.substring(0, 10)}...</p>
                    `;
                    localStorage.setItem('token', data.access_token);
                } else {
                    userInfoElement.innerHTML = `
                        <p class="error">Login failed: ${data.detail || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                userInfoElement.innerHTML = `
                    <p class="error">Error: ${error.message}</p>
                `;
                responseElement.textContent = error.message;
            }
        });
        
        // Get current user
        document.getElementById('getCurrentUser').addEventListener('click', async () => {
            const userInfoElement = document.getElementById('userInfo');
            const responseElement = document.getElementById('apiResponse');
            const token = localStorage.getItem('token');
            
            userInfoElement.innerHTML = 'Fetching user info...';
            
            try {
                // Try the actual backend first
                try {
                    const response = await fetch(`${BACKEND_URL}/api/v1/auth/me`, {
                        method: 'GET',
                        headers: {
                            'Authorization': token ? `Bearer ${token}` : '',
                        },
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        userInfoElement.innerHTML = `
                            <p class="success">User retrieved from main backend</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `;
                        responseElement.textContent = JSON.stringify(data, null, 2);
                        return;
                    }
                } catch (error) {
                    console.log('Main backend error:', error);
                }
                
                // If that fails, try the minimal backend
                const minimalResponse = await fetch(`${MINIMAL_BACKEND_URL}/api/v1/auth/me`, {
                    method: 'GET',
                });
                
                if (minimalResponse.ok) {
                    const data = await minimalResponse.json();
                    userInfoElement.innerHTML = `
                        <p class="success">User retrieved from minimal backend</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <p class="error">Note: Main backend failed, using minimal backend instead.</p>
                    `;
                    responseElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    const data = await minimalResponse.text();
                    userInfoElement.innerHTML = `
                        <p class="error">Failed to retrieve user from both backends</p>
                        <pre>${data}</pre>
                    `;
                    responseElement.textContent = data;
                }
            } catch (error) {
                userInfoElement.innerHTML = `
                    <p class="error">Error: ${error.message}</p>
                `;
                responseElement.textContent = error.message;
            }
        });
        
        // Test endpoints
        document.querySelectorAll('.testEndpoint').forEach(button => {
            button.addEventListener('click', async () => {
                const endpoint = button.getAttribute('data-endpoint');
                const resultElement = document.getElementById('endpointResult');
                const responseElement = document.getElementById('apiResponse');
                const token = localStorage.getItem('token');
                
                resultElement.innerHTML = `Testing ${endpoint}...`;
                
                try {
                    // Try the actual backend first
                    try {
                        const response = await fetch(`${BACKEND_URL}${endpoint}`, {
                            method: 'GET',
                            headers: {
                                'Authorization': token ? `Bearer ${token}` : '',
                            },
                        });
                        
                        const status = response.status;
                        const data = await response.text();
                        
                        resultElement.innerHTML = `
                            <p><strong>Status:</strong> <span class="${status < 400 ? 'success' : 'error'}">${status}</span></p>
                            <p><strong>Endpoint:</strong> ${BACKEND_URL}${endpoint}</p>
                        `;
                        
                        try {
                            const jsonData = JSON.parse(data);
                            responseElement.textContent = JSON.stringify(jsonData, null, 2);
                        } catch (e) {
                            responseElement.textContent = data;
                        }
                        
                        return;
                    } catch (error) {
                        console.log('Main backend error:', error);
                    }
                    
                    // If that fails, try the minimal backend
                    try {
                        const minimalResponse = await fetch(`${MINIMAL_BACKEND_URL}${endpoint}`, {
                            method: 'GET',
                        });
                        
                        const status = minimalResponse.status;
                        const data = await minimalResponse.text();
                        
                        resultElement.innerHTML = `
                            <p><strong>Status:</strong> <span class="${status < 400 ? 'success' : 'error'}">${status}</span></p>
                            <p><strong>Endpoint:</strong> ${MINIMAL_BACKEND_URL}${endpoint}</p>
                            <p class="error">Note: Main backend failed, using minimal backend instead.</p>
                        `;
                        
                        try {
                            const jsonData = JSON.parse(data);
                            responseElement.textContent = JSON.stringify(jsonData, null, 2);
                        } catch (e) {
                            responseElement.textContent = data;
                        }
                    } catch (error) {
                        resultElement.innerHTML = `
                            <p class="error">Both backends failed for ${endpoint}</p>
                            <p>${error.message}</p>
                        `;
                        responseElement.textContent = error.message;
                    }
                } catch (error) {
                    resultElement.innerHTML = `
                        <p class="error">Error: ${error.message}</p>
                    `;
                    responseElement.textContent = error.message;
                }
            });
        });
    </script>
</body>
</html> 