# PA-CHECK-MM Synchronization Plan

## Overview

This document outlines a detailed plan for synchronizing the missing components from the Google Drive folder to the GitHub repository for the PA-CHECK-MM project. Based on the comparison report, the GitHub repository is missing critical document processing capabilities that are present in the Google Drive version.

## 1. Files to Copy

The following files need to be copied from the Google Drive folder to the GitHub repository:

| Source File | Destination Path |
|-------------|------------------|
| `/home/<USER>/Downloads/documentation_checklist.py` | `/home/<USER>/github_repo/src/document_processor/documentation_checklist.py` |
| `/home/<USER>/Downloads/documentation_checklist_service.py` | `/home/<USER>/github_repo/src/document_processor/documentation_checklist_service.py` |
| `/home/<USER>/Downloads/documentation_requirements.py` | `/home/<USER>/github_repo/src/document_processor/documentation_requirements.py` |

## 2. Requirements.txt Updates

The GitHub repository's requirements.txt file needs to be updated to include all necessary dependencies for document processing. The following dependencies should be added to `/home/<USER>/github_repo/requirements.txt`:

```
# Document processing
PyPDF2>=3.0.1
pdf2image>=1.16.3
python-docx>=0.8.11
openpyxl>=3.1.2
pandas>=2.2.0
beautifulsoup4>=4.12.2
lxml>=4.9.3
Pillow>=10.1.0
python-magic>=0.4.27
python-magic-bin>=0.4.14; platform_system=='Windows'
pytesseract>=0.3.10
pyclamd>=1.0.2

# OCR Engines
easyocr>=1.7.0
paddleocr>=*******

# Database
SQLAlchemy>=2.0.23
alembic>=1.12.1
psycopg2-binary>=2.9.9
aiosqlite>=0.19.0

# Additional dependencies
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
email-validator>=2.1.0
```

## 3. Structural Changes

The following structural changes are needed to ensure consistency between the two codebases:

1. Create a new directory structure for document processing components:
   ```
   /home/<USER>/github_repo/src/document_processor/documentation/
   ```

2. Update import statements in the copied files to reflect the new directory structure.

3. Ensure proper integration with existing document processing modules in the GitHub repository.

4. Add necessary database models and migrations for document checklist functionality.

## 4. Step-by-Step Implementation Guide

### Step 1: Create Required Directories
```bash
mkdir -p /home/<USER>/github_repo/src/document_processor/documentation
```

### Step 2: Copy Documentation Files
```bash
cp /home/<USER>/Downloads/documentation_checklist.py /home/<USER>/github_repo/src/document_processor/documentation/
cp /home/<USER>/Downloads/documentation_checklist_service.py /home/<USER>/github_repo/src/document_processor/documentation/
cp /home/<USER>/Downloads/documentation_requirements.py /home/<USER>/github_repo/src/document_processor/documentation/
```

### Step 3: Create __init__.py File
```bash
touch /home/<USER>/github_repo/src/document_processor/documentation/__init__.py
```

### Step 4: Update Import Statements

Modify the import statements in the copied files to reflect the new directory structure:

1. In `documentation_checklist_service.py`:
   - Change `from documentation_checklist import ...` to `from src.document_processor.documentation.documentation_checklist import ...`
   - Change `from documentation_requirements import ...` to `from src.document_processor.documentation.documentation_requirements import ...`

2. In `documentation_checklist.py`:
   - Change `from documentation_requirements import ...` to `from src.document_processor.documentation.documentation_requirements import ...`

### Step 5: Update requirements.txt
```bash
# Append the missing dependencies to requirements.txt
cat >> /home/<USER>/github_repo/requirements.txt << 'EOL'

# Document processing
PyPDF2>=3.0.1
pdf2image>=1.16.3
python-docx>=0.8.11
openpyxl>=3.1.2
pandas>=2.2.0
beautifulsoup4>=4.12.2
lxml>=4.9.3
Pillow>=10.1.0
python-magic>=0.4.27
python-magic-bin>=0.4.14; platform_system=='Windows'
pytesseract>=0.3.10
pyclamd>=1.0.2

# OCR Engines
easyocr>=1.7.0
paddleocr>=*******

# Database
SQLAlchemy>=2.0.23
alembic>=1.12.1
psycopg2-binary>=2.9.9
aiosqlite>=0.19.0

# Additional dependencies
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
email-validator>=2.1.0
EOL
```

### Step 6: Create Integration Module

Create a new file `/home/<USER>/github_repo/src/document_processor/documentation_integration.py` to integrate the documentation modules with the existing codebase:

```python
"""
Integration module for FEMA documentation checklist functionality.
"""

from src.document_processor.documentation.documentation_checklist import DocumentationChecklist, DocumentationStatus
from src.document_processor.documentation.documentation_requirements import get_all_requirements, get_category_requirements
from src.document_processor.file_handler import FileHandler

class DocumentationProcessor:
    """
    Processor class that integrates document processing with FEMA documentation requirements.
    """
    
    def __init__(self, file_handler: FileHandler = None):
        self.file_handler = file_handler or FileHandler()
        
    def create_checklist(self, categories):
        """Create a new documentation checklist for specified categories."""
        return DocumentationChecklist(categories)
        
    def get_requirements(self, category=None):
        """Get documentation requirements for a specific category or all categories."""
        if category:
            return get_category_requirements(category)
        return get_all_requirements()
        
    def process_document_for_requirement(self, file_path, category, requirement):
        """Process a document and link it to a specific requirement."""
        # Process document using existing file handler
        document_info = self.file_handler.process_file(file_path)
        
        # Return document info with category and requirement metadata
        return {
            **document_info,
            "category": category,
            "requirement": requirement,
            "status": DocumentationStatus.COMPLETE
        }
```

### Step 7: Update API Endpoints

Create a new file `/home/<USER>/github_repo/src/api/routers/documentation.py` to add API endpoints for documentation checklist functionality:

```python
"""
API endpoints for FEMA documentation checklist functionality.
"""

from fastapi import APIRouter, Depends, File, UploadFile, Form, HTTPException
from typing import List, Optional

from src.document_processor.documentation.documentation_checklist import DocumentationStatus
from src.document_processor.documentation_integration import DocumentationProcessor

router = APIRouter(prefix="/documentation", tags=["documentation"])

@router.get("/requirements")
async def get_requirements(category: Optional[str] = None):
    """Get documentation requirements for a specific category or all categories."""
    processor = DocumentationProcessor()
    return processor.get_requirements(category)

@router.post("/checklist")
async def create_checklist(categories: List[str]):
    """Create a new documentation checklist for specified categories."""
    processor = DocumentationProcessor()
    checklist = processor.create_checklist(categories)
    return {
        "categories": categories,
        "completion_percentages": checklist.get_completion_percentage(),
        "missing_requirements": checklist.get_missing_requirements()
    }

@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    category: str = Form(...),
    requirement: str = Form(...)
):
    """Upload a document and link it to a specific requirement."""
    processor = DocumentationProcessor()
    
    # Save uploaded file
    file_path = f"/tmp/{file.filename}"
    with open(file_path, "wb") as f:
        f.write(await file.read())
    
    # Process document
    try:
        result = processor.process_document_for_requirement(
            file_path, category, requirement
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
```

### Step 8: Update Main API Router

Update `/home/<USER>/github_repo/src/api/routers.py` to include the new documentation router:

```python
# Add this import
from src.api.routers.documentation import router as documentation_router

# Add this line to the existing router includes
app.include_router(documentation_router)
```

## 5. Potential Issues and Resolutions

### Issue 1: Import Conflicts
**Problem**: The imported modules may have dependencies on other modules that are not present in the GitHub repository.

**Resolution**: 
- Check for import errors after copying the files
- Identify missing dependencies and copy additional modules as needed
- Update import statements to match the GitHub repository's structure

### Issue 2: Database Integration
**Problem**: The documentation_checklist_service.py file references database models and services that may not exist in the GitHub repository.

**Resolution**:
- Create necessary database models in the GitHub repository
- Adapt the service to work with the existing database structure
- If SQLAlchemy is not used in the GitHub repository, modify the service to use the existing database access methods

### Issue 3: Dependency Conflicts
**Problem**: The added dependencies may conflict with existing dependencies in the GitHub repository.

**Resolution**:
- Test the application after adding the dependencies
- Resolve version conflicts by specifying compatible versions
- Consider using virtual environments for testing before committing changes

### Issue 4: API Integration
**Problem**: The new API endpoints may conflict with existing endpoints or routing structure.

**Resolution**:
- Review the existing API structure before adding new endpoints
- Ensure consistent naming conventions and URL patterns
- Test API endpoints for conflicts and proper functionality

### Issue 5: Missing Frontend Components
**Problem**: The document processing functionality may require frontend components that are not present in the GitHub repository.

**Resolution**:
- Identify necessary frontend components from the Google Drive version
- Copy and adapt these components to the GitHub repository's frontend structure
- Update frontend API calls to use the new endpoints

## Conclusion

This synchronization plan provides a comprehensive approach to adding the missing document processing capabilities from the Google Drive folder to the GitHub repository. By following these steps, the GitHub repository will be updated to include all the functionality present in the Google Drive version, ensuring consistency between the two codebases.

After implementing these changes, it is recommended to thoroughly test the application to ensure all components work together correctly and that there are no integration issues.
