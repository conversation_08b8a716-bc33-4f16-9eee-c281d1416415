/**
 * ComplianceMax V74 - Universal Startup Script
 * 
 * SHELL-AGNOSTIC solution that works in PowerShell, CMD, and Bash
 * Prevents PowerShell && syntax issues from contaminating Compliance Pods
 * 
 * Usage: node start.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting ComplianceMax V74 - Universal Shell-Agnostic Mode');
console.log('📍 Working Directory:', __dirname);

// Start Frontend Server (Next.js)
console.log('\n🎨 Starting Frontend Server...');
const frontend = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'app'),
  stdio: 'inherit',
  shell: true
});

// Start Backend Server (FastAPI)
console.log('🔧 Starting Backend Server...');
const backend = spawn('python', ['-m', 'uvicorn', 'main:app', '--host', '0.0.0.0', '--port', '8000', '--reload'], {
  cwd: path.join(__dirname, 'api'),
  stdio: 'inherit',
  shell: true
});

// Error handling
frontend.on('error', (err) => {
  console.error('❌ Frontend process error:', err);
  console.error('💡 Ensure npm is installed and app/package.json exists');
});

backend.on('error', (err) => {
  console.error('❌ Backend process error:', err);
  console.error('💡 Ensure Python and uvicorn are installed and api/main.py exists');
});

// Success indicators
frontend.on('spawn', () => {
  console.log('✅ Frontend server process spawned successfully');
});

backend.on('spawn', () => {
  console.log('✅ Backend server process spawned successfully');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down ComplianceMax V74...');
  frontend.kill('SIGINT');
  backend.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Terminating ComplianceMax V74...');
  frontend.kill('SIGTERM');
  backend.kill('SIGTERM');
  process.exit(0);
});

console.log('\n📱 Frontend will be available at: http://localhost:3333');
console.log('🔌 Backend will be available at: http://localhost:8000');
console.log('📚 API Documentation: http://localhost:8000/docs');
console.log('\n⌨️  Press Ctrl+C to stop both servers');
console.log('🔄 This script works in PowerShell, CMD, and Bash!'); 