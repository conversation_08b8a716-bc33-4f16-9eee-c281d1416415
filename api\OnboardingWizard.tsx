import React, { useState, useEffect } from 'react';

interface FEMAProfile {
  femaNumber: string;
  state: string;
  county: string;
  applicantType: string;
  eligibleDisasters: any[];
  selectedDisaster?: string;
  workflow?: any;
  selectedService?: any;
}

interface ServiceTier {
  id: string;
  name: string;
  description: string;
  price: number;
  features: string[];
  recommended?: boolean;
}

// Step 1: FEMA Number Entry
function FEMANumberEntry({ onComplete, profile, setProfile }: any) {
  const [femaNumber, setFemaNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleLookup = async () => {
    if (!femaNumber.trim()) {
      setError('Please enter your FEMA registration number');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Simulate API call - replace with actual backend integration
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock response based on FEMA number pattern
      const mockProfile = {
        femaNumber,
        state: femaNumber.includes('-TX') ? 'TX' : femaNumber.includes('-FL') ? 'FL' : 'AR',
        county: femaNumber.includes('-TX') ? 'Harris' : femaNumber.includes('-FL') ? 'Miami-Dade' : 'Pulaski',
        applicantType: 'Local Government',
        eligibleDisasters: []
      };

      setProfile(mockProfile);
      onComplete(mockProfile);
    } catch (err) {
      setError('Unable to connect to FEMA systems. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-lg mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Enter Your FEMA Registration Number</h2>
        <p className="text-gray-600">
          We'll automatically populate your information and identify eligible services.
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            FEMA Registration Number
          </label>
          <input
            type="text"
            value={femaNumber}
            onChange={(e) => setFemaNumber(e.target.value)}
            placeholder="e.g., FEMA-12345-TX, PA-67890-FL"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            disabled={loading}
          />
          {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>

        <button
          onClick={handleLookup}
          disabled={loading}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Looking up...' : 'Auto-Populate Information'}
        </button>
      </div>

      {profile && (
        <div className="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-4">✓ Information Retrieved</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">State:</span> {profile.state}
            </div>
            <div>
              <span className="font-medium">County:</span> {profile.county}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Step 2: Service Selection
function ServiceSelection({ onComplete, profile }: any) {
  const [selectedTier, setSelectedTier] = useState('');

  const serviceTiers: ServiceTier[] = [
    {
      id: 'basic',
      name: 'Basic Compliance',
      description: 'Essential tools for straightforward projects',
      price: 199,
      features: [
        'Basic PAPPG compliance checklist',
        'Document templates',
        'Email support',
        'Standard reporting'
      ]
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Advanced automation for complex projects',
      price: 499,
      recommended: true,
      features: [
        'Full PAPPG + DRRA compliance',
        'Intelligent workflow automation',
        'Real-time FEMA integration',
        'Advanced reporting',
        'Priority support',
        'Document OCR processing'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Complete solution for large organizations',
      price: 999,
      features: [
        'Everything in Professional',
        'Multi-project management',
        'Custom policy integration',
        'Dedicated account manager',
        'Training & onboarding',
        'API access'
      ]
    }
  ];

  const handleSubscribe = () => {
    const selectedService = serviceTiers.find(tier => tier.id === selectedTier);
    onComplete({ ...profile, selectedService });
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Service Level</h2>
        <p className="text-gray-600">
          Select the plan that best fits your compliance needs.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {serviceTiers.map((tier) => (
          <div
            key={tier.id}
            className={`relative p-8 border-2 rounded-xl cursor-pointer transition-all ${
              selectedTier === tier.id ? 'border-blue-500 shadow-lg' : 'border-gray-200'
            }`}
            onClick={() => setSelectedTier(tier.id)}
          >
            {tier.recommended && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Recommended
                </span>
              </div>
            )}

            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-gray-900 mb-2">{tier.name}</h3>
              <p className="text-gray-600 text-sm mb-4">{tier.description}</p>
              <div className="text-3xl font-bold text-gray-900">
                ${tier.price}
                <span className="text-lg font-normal text-gray-500">/project</span>
              </div>
            </div>

            <ul className="space-y-3 mb-8">
              {tier.features.map((feature, index) => (
                <li key={index} className="flex items-center text-sm">
                  <span className="w-4 h-4 text-green-500 mr-3">✓</span>
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>

            <button
              className={`w-full py-3 px-6 rounded-lg transition-colors ${
                selectedTier === tier.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {selectedTier === tier.id ? 'Selected' : 'Select Plan'}
            </button>
          </div>
        ))}
      </div>

      {selectedTier && (
        <div className="mt-12 text-center">
          <button
            onClick={handleSubscribe}
            className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-4 px-12 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-cyan-700 transition-all shadow-lg"
          >
            Subscribe & Launch Dashboard
          </button>
          <p className="mt-4 text-sm text-gray-500">
            30-day money-back guarantee • Cancel anytime
          </p>
        </div>
      )}
    </div>
  );
}

// Main Onboarding Component
export default function OnboardingWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [profile, setProfile] = useState<FEMAProfile | null>(null);

  const steps = [
    {
      id: 'fema-lookup',
      title: 'FEMA Registration',
      description: 'Auto-populate your information',
      component: FEMANumberEntry
    },
    {
      id: 'service-selection',
      title: 'Service Level',
      description: 'Select your subscription tier',
      component: ServiceSelection
    }
  ];

  const handleStepComplete = (data: any) => {
    setProfile(data);
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding - redirect to dashboard
      window.location.href = '/dashboard';
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Progress Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">ComplianceMax Setup</h1>
            <span className="text-sm text-gray-500">
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>

          {/* Step Indicators */}
          <div className="flex justify-between mt-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex flex-col items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                  }`}
                >
                  {index + 1}
                </div>
                <div className="mt-2 text-center">
                  <p className="text-sm font-medium text-gray-900">{step.title}</p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-6xl mx-auto px-6 py-12">
        <CurrentStepComponent
          onComplete={handleStepComplete}
          profile={profile}
          setProfile={setProfile}
        />
      </div>
    </div>
  );
} 