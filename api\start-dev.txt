# ComplianceMax V74 Development Startup Script
# Purpose: Start dependencies, servers, and verify accessibility

# Set error handling
$ErrorActionPreference = "Stop"

# Define project root
$ProjectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
$AppDir = Join-Path $ProjectRoot "app"

# Step 1: Check and start Docker dependencies
Write-Host "Starting Docker dependencies (PostgreSQL, Redis)..." -ForegroundColor Cyan
try {
    docker-compose up -d postgres redis
    Start-Sleep -Seconds 5  # Wait for services to initialize
    Write-Host "Docker services started successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to start Docker services: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Verify environment variables
$EnvFile = Join-Path $AppDir ".env"
if (-not (Test-Path $EnvFile)) {
    Write-Host "Copying ENV-EXAMPLE.txt to .env..." -ForegroundColor Yellow
    Copy-Item -Path (Join-Path $ProjectRoot "ENV-EXAMPLE.txt") -Destination $EnvFile
}
Write-Host "Environment variables configured" -ForegroundColor Green

# Step 3: Start Next.js frontend
Write-Host "Starting Next.js frontend on port 3333..." -ForegroundColor Cyan
Set-Location -Path $AppDir
Start-Process -FilePath "npm" -ArgumentList "run dev" -NoNewWindow -RedirectStandardOutput "frontend.log" -RedirectStandardError "frontend.err"
Start-Sleep -Seconds 5  # Wait for server to start

# Step 4: Start FastAPI backend
Write-Host "Starting FastAPI backend on port 8000..." -ForegroundColor Cyan
$ApiDir = Join-Path $ProjectRoot "api"
if (Test-Path $ApiDir) {
    Set-Location -Path $ApiDir
    Start-Process -FilePath "python" -ArgumentList "-m uvicorn main:app --host 0.0.0.0 --port 8000 --reload" -NoNewWindow -RedirectStandardOutput "backend.log" -RedirectStandardError "backend.err"
    Start-Sleep -Seconds 5  # Wait for server to start
} else {
    Write-Host "Warning: API directory not found. Backend startup skipped." -ForegroundColor Yellow
}

# Step 5: Verify server accessibility
Write-Host "Verifying server accessibility..." -ForegroundColor Cyan
$FrontendUrl = "http://localhost:3333"
$BackendUrl = "http://localhost:8000/health"

try {
    $FrontendResponse = Invoke-WebRequest -Uri $FrontendUrl -TimeoutSec 10
    if ($FrontendResponse.StatusCode -eq 200) {
        Write-Host "Frontend server is running at $FrontendUrl" -ForegroundColor Green
    }
} catch {
    Write-Host "Frontend server failed: $_" -ForegroundColor Red
    Get-Content -Path (Join-Path $AppDir "frontend.err") -ErrorAction SilentlyContinue
    exit 1
}

if (Test-Path $ApiDir) {
    try {
        $BackendResponse = Invoke-WebRequest -Uri $BackendUrl -TimeoutSec 10
        if ($BackendResponse.StatusCode -eq 200) {
            Write-Host "Backend server is running at $BackendUrl" -ForegroundColor Green
        }
    } catch {
        Write-Host "Backend server failed: $_" -ForegroundColor Red
        Get-Content -Path (Join-Path $ApiDir "backend.err") -ErrorAction SilentlyContinue
        exit 1
    }
}

# Step 6: Final status
Write-Host "Development environment started successfully!" -ForegroundColor Green
Write-Host "Frontend: $FrontendUrl" -ForegroundColor Green
Write-Host "Backend: $BackendUrl" -ForegroundColor Green
Write-Host "Logs available in app/frontend.log and api/backend.log" -ForegroundColor Yellow