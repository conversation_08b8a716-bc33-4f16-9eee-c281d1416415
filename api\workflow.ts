import { Node, Edge, NodeProps } from 'reactflow';

// ============================================================================
// CORE WORKFLOW TYPES
// ============================================================================

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  category: WorkflowCategory;
  femaProgram?: FEMAProgram;
  status: WorkflowStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  tags: string[];
  metadata: Record<string, any>;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  startNodeId: string;
  variables: WorkflowVariable[];
  rules: WorkflowRule[];
  permissions: WorkflowPermissions;
}

export interface WorkflowInstance {
  id: string;
  workflowDefinitionId: string;
  name: string;
  status: WorkflowInstanceStatus;
  currentNodeId: string;
  startedAt: Date;
  completedAt?: Date;
  context: Record<string, any>;
  history: WorkflowInstanceHistory[];
  assignedTo?: string;
  priority: Priority;
  dueDate?: Date;
  tags: string[];
  metadata: Record<string, any>;
}

export interface WorkflowNode extends Node {
  id: string;
  type: WorkflowNodeType;
  position: { x: number; y: number };
  data: WorkflowNodeData;
  style?: Record<string, any>;
  className?: string;
  targetPosition?: string;
  sourcePosition?: string;
}

export interface WorkflowEdge extends Edge {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: WorkflowEdgeData;
  conditions?: WorkflowCondition[];
  label?: string;
  style?: Record<string, any>;
}

// ============================================================================
// WORKFLOW NODE TYPES AND DATA
// ============================================================================

export type WorkflowNodeType = 
  | 'start'
  | 'end'
  | 'task'
  | 'approval'
  | 'decision'
  | 'document'
  | 'calculation'
  | 'integration'
  | 'notification'
  | 'timer'
  | 'parallel'
  | 'merge'
  | 'fema-eligibility'
  | 'fema-cost-calc'
  | 'fema-review'
  | 'fema-submission';

export interface WorkflowNodeData {
  label: string;
  description?: string;
  config: Record<string, any>;
  validations?: Validation[];
  assignments?: Assignment[];
  notifications?: Notification[];
  integrations?: Integration[];
  femaConfig?: FEMANodeConfig;
  customFields?: CustomField[];
}

export interface WorkflowEdgeData {
  label?: string;
  conditions?: WorkflowCondition[];
  priority?: number;
  timeout?: number;
  retryPolicy?: RetryPolicy;
}

// ============================================================================
// FEMA-SPECIFIC TYPES
// ============================================================================

export type FEMAProgram = 
  | 'PUBLIC_ASSISTANCE'
  | 'HAZARD_MITIGATION'
  | 'INDIVIDUAL_ASSISTANCE'
  | 'DISASTER_RELIEF_FUND'
  | 'FIRE_MANAGEMENT';

export type FEMACategory = 
  | 'CATEGORY_A' // Debris Removal
  | 'CATEGORY_B' // Emergency Protective Measures
  | 'CATEGORY_C' // Roads and Bridges
  | 'CATEGORY_D' // Water Control Facilities
  | 'CATEGORY_E' // Buildings and Equipment
  | 'CATEGORY_F' // Utilities
  | 'CATEGORY_G' // Parks, Recreation, Other
  | 'CATEGORY_Z'; // State Management Costs

export interface FEMANodeConfig {
  program: FEMAProgram;
  category?: FEMACategory;
  formTypes?: string[];
  eligibilityRules?: EligibilityRule[];
  costCalculations?: CostCalculation[];
  requiredDocuments?: RequiredDocument[];
  reviewCriteria?: ReviewCriteria[];
  complianceChecks?: ComplianceCheck[];
}

export interface EligibilityRule {
  id: string;
  name: string;
  description: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
}

export interface CostCalculation {
  id: string;
  name: string;
  formula: string;
  variables: string[];
  validations: string[];
  outputField: string;
}

export interface RequiredDocument {
  id: string;
  name: string;
  description: string;
  type: DocumentType;
  required: boolean;
  template?: string;
  validations?: DocumentValidation[];
}

export interface ReviewCriteria {
  id: string;
  name: string;
  description: string;
  weight: number;
  passingScore: number;
  criteria: string[];
}

export interface ComplianceCheck {
  id: string;
  name: string;
  regulation: string;
  description: string;
  checkType: 'AUTOMATIC' | 'MANUAL' | 'HYBRID';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  remediation?: string;
}

// ============================================================================
// WORKFLOW CONDITIONS AND RULES
// ============================================================================

export interface WorkflowCondition {
  id: string;
  name: string;
  expression: string;
  operator: ConditionOperator;
  value: any;
  field: string;
  logicalOperator?: 'AND' | 'OR';
}

export type ConditionOperator = 
  | 'EQUALS'
  | 'NOT_EQUALS'
  | 'GREATER_THAN'
  | 'LESS_THAN'
  | 'GREATER_THAN_OR_EQUAL'
  | 'LESS_THAN_OR_EQUAL'
  | 'CONTAINS'
  | 'NOT_CONTAINS'
  | 'IN'
  | 'NOT_IN'
  | 'IS_NULL'
  | 'IS_NOT_NULL'
  | 'REGEX'
  | 'DATE_BEFORE'
  | 'DATE_AFTER'
  | 'DATE_BETWEEN';

export interface WorkflowRule {
  id: string;
  name: string;
  description: string;
  conditions: WorkflowCondition[];
  actions: WorkflowAction[];
  priority: number;
  enabled: boolean;
  scope: 'GLOBAL' | 'NODE' | 'EDGE';
  triggers: RuleTrigger[];
}

export interface WorkflowAction {
  id: string;
  type: ActionType;
  config: Record<string, any>;
  order: number;
}

export type ActionType = 
  | 'SET_VARIABLE'
  | 'SEND_NOTIFICATION'
  | 'CALL_API'
  | 'UPDATE_DATABASE'
  | 'GENERATE_DOCUMENT'
  | 'ASSIGN_TASK'
  | 'ESCALATE'
  | 'LOG_EVENT'
  | 'CALCULATE_COST'
  | 'VALIDATE_DOCUMENT';

export type RuleTrigger = 
  | 'NODE_ENTER'
  | 'NODE_EXIT'
  | 'EDGE_TRAVERSE'
  | 'VARIABLE_CHANGE'
  | 'TIME_BASED'
  | 'EXTERNAL_EVENT';

// ============================================================================
// SUPPORTING TYPES
// ============================================================================

export type WorkflowCategory = 
  | 'FEMA_COMPLIANCE'
  | 'DOCUMENT_PROCESSING'
  | 'APPROVAL_PROCESS'
  | 'DATA_COLLECTION'
  | 'REPORTING'
  | 'INTEGRATION'
  | 'CUSTOM';

export type WorkflowStatus = 
  | 'DRAFT'
  | 'ACTIVE'
  | 'INACTIVE'
  | 'ARCHIVED'
  | 'DEPRECATED';

export type WorkflowInstanceStatus = 
  | 'PENDING'
  | 'RUNNING'
  | 'PAUSED'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'ESCALATED';

export type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export type DocumentType = 
  | 'PDF'
  | 'WORD'
  | 'EXCEL'
  | 'IMAGE'
  | 'FORM'
  | 'REPORT'
  | 'CERTIFICATE'
  | 'INVOICE'
  | 'CONTRACT';

export interface WorkflowVariable {
  id: string;
  name: string;
  type: VariableType;
  defaultValue?: any;
  required: boolean;
  description?: string;
  validations?: Validation[];
  scope: 'GLOBAL' | 'NODE' | 'EDGE';
}

export type VariableType = 
  | 'STRING'
  | 'NUMBER'
  | 'BOOLEAN'
  | 'DATE'
  | 'DATETIME'
  | 'ARRAY'
  | 'OBJECT'
  | 'FILE'
  | 'CURRENCY';

export interface Validation {
  type: ValidationType;
  value?: any;
  message: string;
  severity: 'ERROR' | 'WARNING' | 'INFO';
}

export type ValidationType = 
  | 'REQUIRED'
  | 'MIN_LENGTH'
  | 'MAX_LENGTH'
  | 'PATTERN'
  | 'MIN_VALUE'
  | 'MAX_VALUE'
  | 'EMAIL'
  | 'URL'
  | 'DATE_FORMAT'
  | 'CUSTOM';

export interface Assignment {
  type: 'USER' | 'ROLE' | 'GROUP' | 'RULE';
  value: string;
  conditions?: WorkflowCondition[];
}

export interface Notification {
  type: 'EMAIL' | 'SMS' | 'PUSH' | 'SLACK' | 'WEBHOOK';
  template: string;
  recipients: string[];
  conditions?: WorkflowCondition[];
  delay?: number;
}

export interface Integration {
  type: IntegrationType;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  payload?: Record<string, any>;
  authentication?: AuthenticationConfig;
  retryPolicy?: RetryPolicy;
}

export type IntegrationType = 
  | 'REST_API'
  | 'SOAP'
  | 'GRAPHQL'
  | 'DATABASE'
  | 'FILE_SYSTEM'
  | 'FEMA_PORTAL'
  | 'ERP_SYSTEM'
  | 'EMAIL_SERVICE';

export interface AuthenticationConfig {
  type: 'NONE' | 'BASIC' | 'BEARER' | 'API_KEY' | 'OAUTH2' | 'MTLS';
  credentials: Record<string, string>;
}

export interface RetryPolicy {
  maxAttempts: number;
  backoffStrategy: 'FIXED' | 'LINEAR' | 'EXPONENTIAL';
  baseDelay: number;
  maxDelay: number;
}

export interface CustomField {
  id: string;
  name: string;
  type: VariableType;
  label: string;
  placeholder?: string;
  helpText?: string;
  required: boolean;
  validations?: Validation[];
  options?: { label: string; value: any }[];
  dependsOn?: string[];
}

export interface WorkflowPermissions {
  view: string[];
  edit: string[];
  execute: string[];
  admin: string[];
}

export interface WorkflowInstanceHistory {
  id: string;
  timestamp: Date;
  action: string;
  nodeId?: string;
  userId: string;
  details: Record<string, any>;
  status: WorkflowInstanceStatus;
}

export interface DocumentValidation {
  type: 'SIZE' | 'FORMAT' | 'CONTENT' | 'SIGNATURE' | 'ENCRYPTION';
  criteria: Record<string, any>;
  required: boolean;
}

// ============================================================================
// WORKFLOW DESIGNER TYPES
// ============================================================================

export interface WorkflowDesignerState {
  workflowDefinition: WorkflowDefinition;
  selectedNode?: WorkflowNode;
  selectedEdge?: WorkflowEdge;
  isEditing: boolean;
  isDirty: boolean;
  history: WorkflowDefinition[];
  historyIndex: number;
  clipboard?: {
    type: 'NODE' | 'EDGE' | 'SELECTION';
    data: any;
  };
  validationErrors: ValidationError[];
  collaborators: Collaborator[];
}

export interface ValidationError {
  id: string;
  type: 'ERROR' | 'WARNING';
  message: string;
  nodeId?: string;
  edgeId?: string;
  field?: string;
}

export interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  cursor?: { x: number; y: number };
  selectedNodes: string[];
  isOnline: boolean;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface WorkflowAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export * from './workflow'; 