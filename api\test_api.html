<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0,0,0,.1);
            border-radius: 50%;
            border-top-color: #4CAF50;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success-bg {
            background-color: #d4edda;
        }
        .error-bg {
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>ComplianceMax API Connection Tester</h1>
    <p>This standalone page tests connections to backend services.</p>
    
    <div class="panel">
        <h2>Service Health Check</h2>
        <div>
            <button id="testMinimal">Test Minimal Backend (9000)</button>
            <button id="testMain">Test Main Backend (8000)</button>
            <button id="testFrontend">Test Frontend (3000)</button>
        </div>
        <div id="healthStatus" class="status"></div>
    </div>
    
    <div class="container">
        <div class="panel">
            <h2>Authentication</h2>
            <p>Test user authentication endpoints:</p>
            <button id="testAuth">Test Auth Endpoint</button>
            <div id="authStatus" class="status"></div>
        </div>
        
        <div class="panel">
            <h2>Test All APIs</h2>
            <p>Check all API endpoints at once:</p>
            <button id="testAll">Test All Endpoints</button>
            <div id="allStatus" class="status"></div>
        </div>
    </div>
    
    <div class="panel">
        <h2>Response Data</h2>
        <pre id="responseData">No data yet. Click one of the test buttons above.</pre>
    </div>
    
    <div class="panel">
        <h2>Troubleshooting Tips</h2>
        <ol>
            <li>If no services respond, ensure the terminal windows are still running</li>
            <li>Check the backend terminal window for error logs</li>
            <li>Verify ports 8000, 9000, and 3000 are not blocked by firewall or other applications</li>
            <li>Try running <code>python run.py</code> in a terminal window</li>
            <li>Ensure database connection is working correctly</li>
        </ol>
        <p><strong>Note:</strong> The minimal backend should always work, even if the main app has issues.</p>
    </div>

    <script>
        // Helper functions
        function showLoading(elementId) {
            const elem = document.getElementById(elementId);
            elem.innerHTML = '<div class="loading"></div> Testing...';
            elem.className = 'status';
        }
        
        function showSuccess(elementId, message) {
            const elem = document.getElementById(elementId);
            elem.innerHTML = `<span class="success">✓</span> ${message}`;
            elem.className = 'status success-bg';
        }
        
        function showError(elementId, message) {
            const elem = document.getElementById(elementId);
            elem.innerHTML = `<span class="error">✗</span> ${message}`;
            elem.className = 'status error-bg';
        }
        
        function updateResponse(data) {
            const elem = document.getElementById('responseData');
            if (typeof data === 'object') {
                elem.textContent = JSON.stringify(data, null, 2);
            } else {
                elem.textContent = data;
            }
        }
        
        async function testEndpoint(url, timeout = 5000) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);
            
            try {
                const response = await fetch(url, { 
                    signal: controller.signal,
                    headers: { 'Accept': 'application/json' }
                });
                clearTimeout(timeoutId);
                
                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = text;
                }
                
                return {
                    ok: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                clearTimeout(timeoutId);
                return {
                    ok: false,
                    status: 'Error',
                    data: error.message
                };
            }
        }
        
        // Test health endpoints
        document.getElementById('testMinimal').addEventListener('click', async () => {
            showLoading('healthStatus');
            const result = await testEndpoint('http://localhost:9000/api/v1/health');
            updateResponse(result.data);
            
            if (result.ok) {
                showSuccess('healthStatus', `Minimal backend is running! (Status: ${result.status})`);
            } else {
                showError('healthStatus', `Minimal backend is not responding (${result.data})`);
            }
        });
        
        document.getElementById('testMain').addEventListener('click', async () => {
            showLoading('healthStatus');
            const result = await testEndpoint('http://localhost:8000/api/v1/health');
            updateResponse(result.data);
            
            if (result.ok) {
                showSuccess('healthStatus', `Main backend is running! (Status: ${result.status})`);
            } else {
                showError('healthStatus', `Main backend is not responding (${result.data})`);
            }
        });
        
        document.getElementById('testFrontend').addEventListener('click', async () => {
            showLoading('healthStatus');
            const result = await testEndpoint('http://localhost:3000');
            
            if (result.ok) {
                showSuccess('healthStatus', `Frontend is running! (Status: ${result.status})`);
            } else {
                showError('healthStatus', `Frontend is not responding (${result.data})`);
            }
        });
        
        // Test auth endpoint
        document.getElementById('testAuth').addEventListener('click', async () => {
            showLoading('authStatus');
            
            // Try main backend first
            let result = await testEndpoint('http://localhost:8000/api/v1/auth/me');
            if (!result.ok) {
                // Fall back to minimal backend
                result = await testEndpoint('http://localhost:9000/api/v1/auth/me');
            }
            
            updateResponse(result.data);
            
            if (result.ok) {
                showSuccess('authStatus', `Auth endpoint working (found user: ${result.data.username || 'test_user'})`);
            } else {
                showError('authStatus', `Auth endpoint failed (${result.data})`);
            }
        });
        
        // Test all endpoints
        document.getElementById('testAll').addEventListener('click', async () => {
            showLoading('allStatus');
            
            const endpoints = [
                { url: 'http://localhost:9000/api/v1/health', name: 'Minimal Health' },
                { url: 'http://localhost:8000/api/v1/health', name: 'Main Health' },
                { url: 'http://localhost:3000', name: 'Frontend' },
                { url: 'http://localhost:8000/api/v1/auth/me', name: 'Auth' },
            ];
            
            const results = [];
            
            for (const endpoint of endpoints) {
                const result = await testEndpoint(endpoint.url);
                results.push({
                    name: endpoint.name,
                    url: endpoint.url,
                    status: result.status,
                    ok: result.ok
                });
            }
            
            const summary = results.map(r => `${r.name} (${r.url}): ${r.ok ? '✓' : '✗'} ${r.status}`).join('\n');
            updateResponse(summary);
            
            const working = results.filter(r => r.ok).length;
            const total = results.length;
            
            if (working === total) {
                showSuccess('allStatus', `All ${total} endpoints are working!`);
            } else if (working > 0) {
                showSuccess('allStatus', `${working} of ${total} endpoints are working. See details below.`);
            } else {
                showError('allStatus', `All endpoints failed. Check if services are running.`);
            }
        });
        
        // Auto-run the test all on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('testAll').click();
            }, 500);
        });
    </script>
</body>
</html> 