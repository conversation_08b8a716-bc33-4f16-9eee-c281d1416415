[{"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Disaster Declaration", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1a66c9c5-1bc3-45f1-a3f0-59786fc4b217", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "grok_tag": "GROK.General", "step/requirement": "FEMA Issues Declaration and Designates Areas", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "162f1002-d0d6-4a77-a92d-9771bb163ae8", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "grok_tag": "GROK.General", "step/requirement": "Determine Incident Period and Deadlines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ffc3ed3d-bf29-4aa6-b834-6eeba2199936", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "grok_tag": "GROK.General", "step/requirement": "Assess Applicant Eligibility", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "851d9ed0-1c0b-4c66-9986-4bd65bbbe9cc", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "grok_tag": "GROK.General", "step/requirement": "Determine Facility Eligibility", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a82a4cd0-6e06-43e4-a93b-88fff39adaf2", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_FullChecklist_Phase1.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "84f2ee93-b4c8-42db-9f98-1ef7421493ad", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Disaster Declaration", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "c85b24e5-6ecb-4eb5-9668-a22b53980366", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "grok_tag": "GROK.General", "step/requirement": "FEMA Issues Declaration and Designates Areas", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "b56807e5-7667-449d-bcfe-dbe425632199", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "grok_tag": "GROK.General", "step/requirement": "Determine Incident Period and Deadlines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1614a463-cf37-4075-955d-60a1a41ef094", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "grok_tag": "GROK.General", "step/requirement": "Assess Applicant Eligibility", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "23a322a5-bcc9-4957-89b6-36c83c7719eb", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "grok_tag": "GROK.General", "step/requirement": "Determine Facility Eligibility", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d753ce45-2485-4d87-9f69-a87cd3a9ea36", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "c4da9e30-0df2-4bb4-9477-4f80dd1b2384", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Disaster Declaration", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e2894a79-9981-44c3-a1fa-80f87aec9ff5", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "grok_tag": "GROK.General", "step/requirement": "FEMA Issues Declaration and Designates Areas", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "7600554d-913f-40d5-83f5-a80130768dcd", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "grok_tag": "GROK.General", "step/requirement": "Determine Incident Period and Deadlines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a1d05f9e-a72a-40f4-b824-5d155cc03fc4", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "grok_tag": "GROK.General", "step/requirement": "Assess Applicant Eligibility", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "80115e6a-84b7-4850-b783-1cff219d551a", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "grok_tag": "GROK.General", "step/requirement": "Determine Facility Eligibility", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fd2c85ad-4701-4b07-a0e9-f1ae5ad8ca23", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a339a514-3fb9-40d4-b523-a359227078fa", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Disaster Declaration", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ee0c4597-33c0-4b88-8c24-48a21f0c5cba", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "grok_tag": "GROK.General", "step/requirement": "FEMA Issues Declaration and Designates Areas", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "9a257db0-8def-47ad-a3b9-ea27000f8515", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "grok_tag": "GROK.General", "step/requirement": "Determine Incident Period and Deadlines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "4a4fd1e1-6a13-4dc3-84c8-2d735176accd", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "grok_tag": "GROK.General", "step/requirement": "Assess Applicant Eligibility", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e5d610a7-34e0-4a06-beba-7c2bc8133f8a", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "grok_tag": "GROK.General", "step/requirement": "Determine Facility Eligibility", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "48c57632-bef7-4de7-9640-94d6c808ddc1", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d1a6a5ab-b29e-41bb-9ed6-ac6bb56a3026", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Disaster Declaration", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "528ba73e-c2af-426c-9253-616b29ef424f", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "grok_tag": "GROK.General", "step/requirement": "FEMA Issues Declaration and Designates Areas", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "cbe65b36-7267-4514-add3-4a1cb8e371f5", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "grok_tag": "GROK.General", "step/requirement": "Determine Incident Period and Deadlines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "926f5cd9-9229-44f1-aebd-a2def9deaac5", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "grok_tag": "GROK.General", "step/requirement": "Assess Applicant Eligibility", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1928d792-12d1-4357-99e3-f076ec5d4024", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "grok_tag": "GROK.General", "step/requirement": "Determine Facility Eligibility", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "aa804fd1-492d-414d-8eb1-bc4ac6d16aea", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "b6c3b6b0-39a4-44ba-bcd5-053f48fb54d6", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "An incident occurs that exceeds state/local capacity", "action_required_then": "Governor/Tribal Leader submits a request for federal assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Disaster Declaration", "documentation_required": "Governor's letter, damage assessments, cost estimates, impact statements", "responsible_party": "State/Tribe/Territory Government", "notes": "Request must demonstrate need, type of aid required, and affected areas", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "Stafford Act §401; PAPPG Ch. 2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "cde8ef3d-f205-4e43-8bd1-6986db4e9d40", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration request is approved", "action_required_then": "FEMA defines the eligible incident period, counties, and categories of assistance", "grok_tag": "GROK.General", "step/requirement": "FEMA Issues Declaration and Designates Areas", "documentation_required": "Federal Register Notice, FEMA Declaration Memo", "responsible_party": "FEMA", "notes": "Sets the boundaries for eligible PA work and timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a3216ad2-3f80-43b2-b49a-66caef743055", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Declaration issued", "action_required_then": "Record the incident start and end dates; calculate submission timelines", "grok_tag": "GROK.General", "step/requirement": "Determine Incident Period and Deadlines", "documentation_required": "Disaster-specific fact sheet, Federal Register", "responsible_party": "Recipient and Applicant", "notes": "Deadlines for RPA, project submission, and work completion depend on these dates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "44 CFR §206.32(f); PAPPG Ch. 2 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d2bde91c-c137-43d3-b244-23501561470d", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Entity intends to apply for assistance", "action_required_then": "Determine if the entity is a state, local government, tribe, or eligible PNP", "grok_tag": "GROK.General", "step/requirement": "Assess Applicant Eligibility", "documentation_required": "Legal charter, articles of incorporation, proof of tax status, bylaws", "responsible_party": "FEMA and Recipient", "notes": "PNPs must provide critical or essential services and meet facility eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § I; 44 CFR §206.222", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "95053512-0642-4a3d-b1c5-b893861d47a2", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Applicant has physical locations affected by disaster", "action_required_then": "Confirm that facilities are eligible under PNP or government rules", "grok_tag": "GROK.General", "step/requirement": "Determine Facility Eligibility", "documentation_required": "Deeds, leases, maintenance records, pre-disaster photos", "responsible_party": "Applicant, reviewed by FEMA", "notes": "Must demonstrate legal responsibility and active use at time of incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III; Table 8–9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "52ebecd4-f376-4c2c-8ccf-0716c1892f89", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declaration and Initial Eligibility", "trigger_condition_if": "Work is claimed under PA", "action_required_then": "Confirm work is emergency (Cat A/B) or permanent (Cat C–G) and is eligible", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work orders, photos, logs, mutual aid agreements", "responsible_party": "Applicant and FEMA", "notes": "Work must be required due to the incident and within the designated area", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–II; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "52fbaf03-abaa-46c6-97d7-097b9bf1b515", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "grok_tag": "GROK.General", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "7a297ab7-1f5e-4c45-8ba6-4f304ec44334", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "grok_tag": "GROK.General", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "9518e8d6-a611-47de-a70f-3b175288fa6f", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "grok_tag": "GROK.General", "step/requirement": "Declaration Request Submission", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "36c1ef72-049d-467c-b9be-a66f5345d2dd", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "grok_tag": "GROK.General", "step/requirement": "Declaration Evaluation by FEMA", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fca185b6-ffb7-467e-bd98-015e65d10663", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Declaration Determination", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase1.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "b7f5a621-5c43-47c1-81a8-c700d58085d1", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "grok_tag": "GROK.General", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "65ee4b79-06ad-4d11-bd0c-352ab7094558", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "grok_tag": "GROK.General", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "929bd1df-9ad7-4bbd-9ee1-245f496b7bfd", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "grok_tag": "GROK.General", "step/requirement": "Declaration Request Submission", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "07e1e78a-5c8f-423a-b407-bee0f2775959", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "grok_tag": "GROK.General", "step/requirement": "Declaration Evaluation by FEMA", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "9a75bfe7-3582-4149-ae24-923669b3fd4e", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Declaration Determination", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "7eacd2fb-0485-4762-ae62-fdd9e60d96ae", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "grok_tag": "GROK.General", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "6ca4960d-42c9-4ba7-a288-cae70a939d45", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "grok_tag": "GROK.General", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "06b7e559-febd-476e-919d-017c637a8a5f", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "grok_tag": "GROK.General", "step/requirement": "Declaration Request Submission", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "4b1de57e-e3df-4d73-9aa1-4030ff76c4e6", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "grok_tag": "GROK.General", "step/requirement": "Declaration Evaluation by FEMA", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "7d3b25d5-8563-4283-b5fb-002c7b8d27a6", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Declaration Determination", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fdb4e537-8dd2-4ae2-8223-e64ca83a293c", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "grok_tag": "GROK.General", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "78f25347-4dd3-4361-8a89-b4e327590193", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "grok_tag": "GROK.General", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ef76faa2-2f45-49a9-b805-1d16ed6905de", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "grok_tag": "GROK.General", "step/requirement": "Declaration Request Submission", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ad50dc81-ccf7-4db7-bc09-b525c4677cb2", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "grok_tag": "GROK.General", "step/requirement": "Declaration Evaluation by FEMA", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ec8d37ba-104e-413f-bcfc-154c14c47a5c", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Declaration Determination", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fdba74ef-1470-450a-bb85-4aabaec86c91", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Incident causes widespread damage", "action_required_then": "Conduct initial assessment and request joint Preliminary Damage Assessment (PDA) from FEMA", "grok_tag": "GROK.General", "step/requirement": "Initial Damage Assessment by State/Tribal/Territorial (STT) Government", "documentation_required": "Local reports, cost estimates, insurance info, infrastructure damage logs", "responsible_party": "STT Government", "applicable_regulations": "44 C.F.R. § 206.33(a)", "notes": "Assessment must show damage exceeds local capacity", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "8551371e-3807-4c98-99a4-90984c63b14b", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "STT requests PDA and FEMA deems it necessary", "action_required_then": "STT and FEMA assess damage together, document eligible damage", "grok_tag": "GROK.General", "step/requirement": "Joint Preliminary Damage Assessment (PDA)", "documentation_required": "Photos, facility damage, location data, cost estimates, insurance documents", "responsible_party": "FEMA and STT officials", "applicable_regulations": "44 C.F.R. § 206.33(b), (d)", "notes": "Used to determine if federal declaration is warranted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "8a9f7d0a-ba0e-4d87-a324-5167d8375ebf", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Damage exceeds local and state capacity", "action_required_then": "Governor or Tribal Chief Executive submits request to the President via FEMA", "grok_tag": "GROK.General", "step/requirement": "Declaration Request Submission", "documentation_required": "Estimated costs, resource needs, declaration form, PDA results", "responsible_party": "Governor or Tribal Chief Executive", "applicable_regulations": "44 C.F.R. §§ 206.35, 206.36", "notes": "Must be submitted within 30 days unless extended", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "83f27773-886c-4c1d-ab03-4f3a5acd7883", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "Declaration request is submitted", "action_required_then": "FEMA evaluates against eligibility criteria including cost per capita, prior disasters, insurance, etc.", "grok_tag": "GROK.General", "step/requirement": "Declaration Evaluation by FEMA", "documentation_required": "PDA reports, impact summaries, insurance records", "responsible_party": "FEMA Regional Administrator", "applicable_regulations": "44 C.F.R. § 206.48", "notes": "Criteria vary slightly for Tribal Nations", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "c27ad635-47d1-4bca-a168-c6aeb7920e60", "grokTags": ["GROK.General"]}, {"phase": 1, "process_phase": "Phase 1: Declarations and Planning", "trigger_condition_if": "FEMA recommends approval", "action_required_then": "President declares a major disaster or emergency and defines incident type, areas, and assistance", "grok_tag": "GROK.General", "step/requirement": "Presidential Declaration Determination", "documentation_required": "Declaration recommendation packet from FEMA", "responsible_party": "President via FEMA", "applicable_regulations": "Stafford Act §§ 401, 403, 406, 502", "notes": "Declaration activates funding and cost shares", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "f9e6234b-ac0a-4b62-a6c9-7e04f979ebd2", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "grok_tag": "GROK.General", "step/requirement": "Applicant Briefing", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a1a1506a-b5ae-431d-990c-d0daf5c002d9", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "5f209f52-194b-4b15-ab4e-402627926705", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "grok_tag": "GROK.General", "step/requirement": "FEMA Review of Applicant Eligibility", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "41475331-6ef8-426a-83ae-43f5f03022fe", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "grok_tag": "GROK.General", "step/requirement": "Facility Eligibility Determination", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "78caaaac-283c-4de4-ae31-2ef400eb5ef8", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "grok_tag": "GROK.General", "step/requirement": "Legal Responsibility Validation", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase2.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "30616228-a207-4224-aff2-bfbd7a062552", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "grok_tag": "GROK.General", "step/requirement": "Applicant Briefing", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "651068f6-13e4-418e-8400-5792e9a64468", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fe94c99a-5054-4876-bbf7-145e8f3c67f1", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "grok_tag": "GROK.General", "step/requirement": "FEMA Review of Applicant Eligibility", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d02162bd-2e45-42ed-9bbd-2dbb24057af5", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "grok_tag": "GROK.General", "step/requirement": "Facility Eligibility Determination", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1ebef827-ae1d-4e4a-9fa5-29462cb34afd", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "grok_tag": "GROK.General", "step/requirement": "Legal Responsibility Validation", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d595d146-f8a8-464e-9af2-a9b66f54bbc6", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "grok_tag": "GROK.General", "step/requirement": "Applicant Briefing", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1e8ce47a-009c-4f77-bff0-0a4a3ac8df83", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a4d19e1c-678d-440e-911d-270358b4396e", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "grok_tag": "GROK.General", "step/requirement": "FEMA Review of Applicant Eligibility", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "471b09cd-5945-4723-8ec9-fdb2ca1087ec", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "grok_tag": "GROK.General", "step/requirement": "Facility Eligibility Determination", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "048af955-389b-471a-9a5e-7416b33dc270", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "grok_tag": "GROK.General", "step/requirement": "Legal Responsibility Validation", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "8367c79c-033b-426d-8163-118eeb218143", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Area is designated for Public Assistance under the declaration", "action_required_then": "FEMA and Recipient conduct briefing to inform potential applicants of eligibility and process", "grok_tag": "GROK.General", "step/requirement": "Applicant Briefing", "documentation_required": "Briefing materials, sign-in sheets, slide decks", "responsible_party": "FEMA and Recipient (State/Tribe/Territory)", "applicable_regulations": "PAPPG v5.0, Chapter 3", "notes": "Participation is critical to understand timelines and documentation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "53852163-20b7-44da-a563-163cea56aa28", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant seeks PA funding", "action_required_then": "Submit RPA via Grants Portal within 30 days of area being designated", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, legal documents, DUNS/SAM verification", "responsible_party": "Applicant", "applicable_regulations": "44 C.F.R. § 206.202", "notes": "Deadline extensions must be requested in writing", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fbf588fb-b7d5-4ff4-8397-a22dd907fa1b", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "RPA is submitted", "action_required_then": "FEMA determines eligibility of applicant based on type, function, legal status", "grok_tag": "GROK.General", "step/requirement": "FEMA Review of Applicant Eligibility", "documentation_required": "Charters, IRS 501(c)(3) letter, facility use data, proof of legal responsibility", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 3, 44 C.F.R. § 206.222", "notes": "PNPs must meet critical or essential service criteria", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "5cbd04a6-dcbf-4818-992b-70fb402317da", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant owns or operates a facility", "action_required_then": "FEMA determines if the facility is eligible under public or PNP criteria", "grok_tag": "GROK.General", "step/requirement": "Facility Eligibility Determination", "documentation_required": "Facility maps, floor plans, leases, maintenance records", "responsible_party": "Applicant and FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4", "notes": "Facilities must be in active use and legally owned or leased", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "247b1f6c-74ba-4579-9681-65b417be2f3c", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: Applicant Eligibility and RPA", "trigger_condition_if": "Applicant claims cost for facility or work", "action_required_then": "Submit documentation proving legal responsibility for facility or site", "grok_tag": "GROK.General", "step/requirement": "Legal Responsibility Validation", "documentation_required": "Deeds, contracts, ordinances, mutual aid agreements", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4, Table 9", "notes": "Work on private property requires proof of public interest", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d3fb050c-15d0-4664-9c0a-b7ae80cfc8ab", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "grok_tag": "GROK.General", "step/requirement": "Attend Applicant Briefing", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "b77b9432-a771-4d72-9aa0-96d8c58e7a82", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "dde6fd26-9dc7-4b56-994f-b784193cf536", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "grok_tag": "GROK.General", "step/requirement": "Assign Program Delivery Manager (PDMG)", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "2429014c-cfdc-48b6-bdec-492ee91010b2", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "grok_tag": "GROK.General", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "b296ed09-207d-4600-a312-441338d0b0a0", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "grok_tag": "GROK.General", "step/requirement": "Submit Damage Inventory (DI)", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a2746d26-ee20-474c-9a6f-7410c5ee413e", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "grok_tag": "GROK.General", "step/requirement": "Attend Applicant Briefing", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "dd207c22-41c4-4369-927e-8b092a47b576", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "89f666d6-aa24-487b-8cbd-50142210737c", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "grok_tag": "GROK.General", "step/requirement": "Assign Program Delivery Manager (PDMG)", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "33d7ae19-1c06-4750-90b6-75f44abf6abd", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "grok_tag": "GROK.General", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "eb312a97-9f19-4a63-b887-6ef87c0c209a", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "grok_tag": "GROK.General", "step/requirement": "Submit Damage Inventory (DI)", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "02877dc9-2b9c-48e1-a636-bb0eef165485", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "grok_tag": "GROK.General", "step/requirement": "Attend Applicant Briefing", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ed5d78c0-9afa-407d-8ff7-d6b504233f58", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "93df4fea-a421-4735-9c1a-395b794bc877", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "grok_tag": "GROK.General", "step/requirement": "Assign Program Delivery Manager (PDMG)", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "83291288-743c-481f-b32c-d38557fc17f1", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "grok_tag": "GROK.General", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "502d6d93-8d32-49e1-a755-45746ff65583", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "grok_tag": "GROK.General", "step/requirement": "Submit Damage Inventory (DI)", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "6a0e89b2-cd5e-46d7-ae06-76a240b19c8d", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "grok_tag": "GROK.General", "step/requirement": "Attend Applicant Briefing", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "51ed889e-9de6-41d7-9691-34e12fffb5bf", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "2f4546de-a357-41ab-a175-9df75fddc010", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "grok_tag": "GROK.General", "step/requirement": "Assign Program Delivery Manager (PDMG)", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "aac7b5c4-d342-4fee-9be7-0e65057a62e7", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "grok_tag": "GROK.General", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1b7c1608-0e3e-488a-b4ce-1b8bbf9d0de2", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "grok_tag": "GROK.General", "step/requirement": "Submit Damage Inventory (DI)", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ce7d7290-7312-4d05-a837-bc56c358330c", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Declaration issued and applicant is eligible", "action_required_then": "Participate in Applicant Briefing conducted by Recipient", "grok_tag": "GROK.General", "step/requirement": "Attend Applicant Briefing", "documentation_required": "Briefing attendance log, presentation materials", "responsible_party": "Recipient and Applicant", "notes": "Briefing explains eligibility, timelines, and application procedures", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § V; 44 CFR §206.207(b)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e92b9407-0ac5-4006-b9d4-6d6401f6132f", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "Applicant has attended briefing", "action_required_then": "Complete and submit RPA in FEMA Grants Portal within 30 days", "grok_tag": "GROK.General", "step/requirement": "Submit Request for Public Assistance (RPA)", "documentation_required": "RPA form, organizational documents, DUNS/UEI, SAM registration", "responsible_party": "Applicant", "notes": "Late submissions require written justification and FEMA approval", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI; 44 CFR §206.202", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "6be23524-a278-4b8e-b72c-8ffed9595ae8", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "FEMA accepts RPA", "action_required_then": "FEMA assigns a PDMG to support project formulation", "grok_tag": "GROK.General", "step/requirement": "Assign Program Delivery Manager (PDMG)", "documentation_required": "FEMA assignment notice, contact info", "responsible_party": "FEMA", "notes": "PDMG serves as liaison throughout PA process", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ff228cf8-ce31-47c6-bd99-72c958e7fdb3", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "PDMG is assigned and RPA accepted", "action_required_then": "FEMA schedules and conducts RSM with applicant", "grok_tag": "GROK.General", "step/requirement": "Develop Recovery Scoping Meeting (RSM) Summary", "documentation_required": "RSM summary, checklist, damage inventory template", "responsible_party": "FEMA and Applicant", "notes": "Identifies all disaster impacts and projects for eligibility review", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d6ebff22-c908-4656-940d-b5ebc62e0d23", "grokTags": ["GROK.General"]}, {"phase": 2, "process_phase": "Phase 2: RPA and Applicant Briefing", "trigger_condition_if": "RSM completed", "action_required_then": "List all disaster-damaged sites and potential projects", "grok_tag": "GROK.General", "step/requirement": "Submit Damage Inventory (DI)", "documentation_required": "DI template, photos, site sketches, cost estimates, location data", "responsible_party": "Applicant", "notes": "DI must be submitted within 60 days of RSM unless extension granted", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 2 § VI", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "658bb72e-c0f8-4448-a665-05d943ba1303", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Document Incident-Related Damages", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "82ca96c2-28f5-4a64-9e05-5d557676b2fc", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "grok_tag": "GROK.General", "step/requirement": "Confirm Facility Eligibility", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "cbc56492-32c7-40fb-a03d-5cc2cb411ecb", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e4dc0a76-3bf3-47b4-95be-30cdaeb781c8", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "grok_tag": "GROK.General", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "af7d7dd7-5621-4f9c-8620-8b573069fad8", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "grok_tag": "GROK.General", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_P2_P3.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d34fddf2-1dd3-4efe-989c-ff8eed0cf470", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Document Incident-Related Damages", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "78103822-6cbd-449d-86e1-6dbd382de7a6", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "grok_tag": "GROK.General", "step/requirement": "Confirm Facility Eligibility", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "8f97e6c0-2c18-499d-bfaf-aea2c6c596f9", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "854a3ac0-e735-4b17-b7a4-5af38d18b8c4", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "grok_tag": "GROK.General", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "f7c49658-7380-4037-bf59-890bb6df76e2", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "grok_tag": "GROK.General", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a8980483-86ec-453d-92da-3f8c8ea58f3f", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Document Incident-Related Damages", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "29f2d019-ef0f-4798-8e84-8854276a8d61", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "grok_tag": "GROK.General", "step/requirement": "Confirm Facility Eligibility", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "ea16160d-3c44-4ea4-938e-fc8cf44bc750", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "96fee3e0-560e-4e50-a920-4519c52574b3", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "grok_tag": "GROK.General", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "296d8570-e373-4597-b289-344901633f34", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "grok_tag": "GROK.General", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "4d6b18c9-ed53-4952-adf9-f9eab8111d35", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Damage inventory includes affected facilities or infrastructure", "action_required_then": "Validate that damages are directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Document Incident-Related Damages", "documentation_required": "Photos, maintenance records, before/after assessments, GPS-tagged images", "responsible_party": "Applicant and FEMA", "notes": "Damage must be within the designated disaster area and incident period", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I.A", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "7976ee12-9908-46f5-9d85-58d67ca9fb33", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Facility is listed in damage inventory", "action_required_then": "Verify that facility is owned/operated by eligible applicant and used for eligible purpose", "grok_tag": "GROK.General", "step/requirement": "Confirm Facility Eligibility", "documentation_required": "Title, deed, lease agreements, insurance records, utility bills", "responsible_party": "Applicant", "notes": "Critical services (hospitals, fire, power) have special requirements for PNPs", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § II–III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "2323af2f-b8a6-417b-ae06-e6072ce1441a", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project includes work claimed as emergency or permanent", "action_required_then": "Categorize work by FEMA PA Category A–G", "grok_tag": "GROK.General", "step/requirement": "Determine Work Eligibility", "documentation_required": "Work descriptions, response logs, site inspection reports", "responsible_party": "Applicant and FEMA", "notes": "Work must be necessary to address damage caused by the disaster", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 4 § I–III; Table 6–7", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "598798ca-d223-4876-9123-d078f388e948", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Project may affect natural resources or historic properties", "action_required_then": "Complete EHP checklist and provide supporting documentation", "grok_tag": "GROK.General", "step/requirement": "Assess Environmental and Historic Preservation (EHP) Impacts", "documentation_required": "Maps, SHPO/THPO coordination, biological surveys, permits", "responsible_party": "Applicant and FEMA EHP staff", "notes": "EHP review must be complete before project obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA; NHPA", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "3773fc97-76b5-451e-a7b7-5042f4eef95a", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage & Facility Eligibility", "trigger_condition_if": "Permanent work is required", "action_required_then": "Identify cost-effective mitigation to reduce future risk", "grok_tag": "GROK.General", "step/requirement": "Determine Hazard Mitigation Opportunities (Section 406)", "documentation_required": "Mitigation proposals, risk analysis, engineering reports", "responsible_party": "Applicant, with FEMA review", "notes": "Mitigation must be technically feasible and environmentally compliant", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a7eabc99-4ec3-42db-b484-d7c3fb0b1fc3", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Facility shows damage", "action_required_then": "Demonstrate damage is directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Damage must result from declared incident", "documentation_required": "Photos, inspection reports, time-stamped logs, incident narratives", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4 & 5, Table 7", "notes": "Damage not caused by the incident is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "5a6be5fd-48c3-42e4-8957-edbc0796b4c4", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Applicant submits project", "action_required_then": "Verify physical location is within the federally declared disaster area", "grok_tag": "GROK.General", "step/requirement": "Facility must be within designated disaster area", "documentation_required": "Maps, GPS data, address verification, damage site photos", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 8", "notes": "Essential to prove eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "f27be2be-3055-4902-ada9-5710a61bc371", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Work is claimed on damaged facility or site", "action_required_then": "Show proof that applicant had legal responsibility at time of disaster", "grok_tag": "GROK.General", "step/requirement": "Establish Legal Responsibility", "documentation_required": "Ownership documents, leases, contracts, jurisdictional maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 9", "notes": "Work done without legal authority is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "89b1f9a6-e20e-4f4e-9e52-32cd099aa07e", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Work is being scoped for eligibility", "action_required_then": "Classify as Emergency (Cat A/B) or Permanent (Cat C-G) work", "grok_tag": "GROK.General", "step/requirement": "Emergency Work vs. Permanent Work Classification", "documentation_required": "Work orders, activity logs, incident response documents", "responsible_party": "Applicant, reviewed by FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4 Section II", "notes": "Incorrect classification can delay funding", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "43c69b8f-eba1-43d3-a0d9-c1545d0aa9b9", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Applicant has multiple damaged sites or assets", "action_required_then": "Group damages into projects and submit impact list", "grok_tag": "GROK.General", "step/requirement": "Submit Impact List and Grouping", "documentation_required": "Impact list template, supporting estimates, photos, maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 5", "notes": "Proper grouping streamlines project formulation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase3.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e20d7a3e-99dc-4dba-976b-39325e5801b2", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Facility shows damage", "action_required_then": "Demonstrate damage is directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Damage must result from declared incident", "documentation_required": "Photos, inspection reports, time-stamped logs, incident narratives", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4 & 5, Table 7", "notes": "Damage not caused by the incident is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1f648560-1bd6-4221-9bc1-11bf289ec090", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Applicant submits project", "action_required_then": "Verify physical location is within the federally declared disaster area", "grok_tag": "GROK.General", "step/requirement": "Facility must be within designated disaster area", "documentation_required": "Maps, GPS data, address verification, damage site photos", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 8", "notes": "Essential to prove eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "cbb8dc78-9826-4bec-a04f-7c18a1b19612", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Work is claimed on damaged facility or site", "action_required_then": "Show proof that applicant had legal responsibility at time of disaster", "grok_tag": "GROK.General", "step/requirement": "Establish Legal Responsibility", "documentation_required": "Ownership documents, leases, contracts, jurisdictional maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 9", "notes": "Work done without legal authority is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "be83b7c8-840a-4d57-8be8-637c3b3f24cf", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Work is being scoped for eligibility", "action_required_then": "Classify as Emergency (Cat A/B) or Permanent (Cat C-G) work", "grok_tag": "GROK.General", "step/requirement": "Emergency Work vs. Permanent Work Classification", "documentation_required": "Work orders, activity logs, incident response documents", "responsible_party": "Applicant, reviewed by FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4 Section II", "notes": "Incorrect classification can delay funding", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "15a47783-092b-4a44-9742-3db3f41bf013", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Applicant has multiple damaged sites or assets", "action_required_then": "Group damages into projects and submit impact list", "grok_tag": "GROK.General", "step/requirement": "Submit Impact List and Grouping", "documentation_required": "Impact list template, supporting estimates, photos, maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 5", "notes": "Proper grouping streamlines project formulation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1204b1e0-b70a-4082-870f-c0add8c1772a", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Facility shows damage", "action_required_then": "Demonstrate damage is directly caused by the declared event", "grok_tag": "GROK.General", "step/requirement": "Damage must result from declared incident", "documentation_required": "Photos, inspection reports, time-stamped logs, incident narratives", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 4 & 5, Table 7", "notes": "Damage not caused by the incident is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "9b521a32-1b86-4cfb-a255-ddfdf607f188", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Applicant submits project", "action_required_then": "Verify physical location is within the federally declared disaster area", "grok_tag": "GROK.General", "step/requirement": "Facility must be within designated disaster area", "documentation_required": "Maps, GPS data, address verification, damage site photos", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 8", "notes": "Essential to prove eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "12527dcc-aee0-4c69-b852-25aed1bf759f", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Work is claimed on damaged facility or site", "action_required_then": "Show proof that applicant had legal responsibility at time of disaster", "grok_tag": "GROK.General", "step/requirement": "Establish Legal Responsibility", "documentation_required": "Ownership documents, leases, contracts, jurisdictional maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Table 9", "notes": "Work done without legal authority is ineligible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e9ca1d3c-7d5f-436f-8b67-a700446f3278", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Work is being scoped for eligibility", "action_required_then": "Classify as Emergency (Cat A/B) or Permanent (Cat C-G) work", "grok_tag": "GROK.General", "step/requirement": "Emergency Work vs. Permanent Work Classification", "documentation_required": "Work orders, activity logs, incident response documents", "responsible_party": "Applicant, reviewed by FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 4 Section II", "notes": "Incorrect classification can delay funding", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "8abd05a2-7afd-406c-aaae-6b71d0516dea", "grokTags": ["GROK.General"]}, {"phase": 3, "process_phase": "Phase 3: Damage and Facility/Work Eligibility", "trigger_condition_if": "Applicant has multiple damaged sites or assets", "action_required_then": "Group damages into projects and submit impact list", "grok_tag": "GROK.General", "step/requirement": "Submit Impact List and Grouping", "documentation_required": "Impact list template, supporting estimates, photos, maps", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 5", "notes": "Proper grouping streamlines project formulation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "5a22f080-c139-4e31-9847-56a313ec3000", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Work has been completed or is ongoing", "action_required_then": "Categorize expenses: labor, equipment, materials, contracts, donations, volunteers", "grok_tag": "GROK.General", "step/requirement": "Identify All Cost Types (Force Account, Contract, Materials)", "documentation_required": "Timesheets, equipment logs, invoices, procurement records", "responsible_party": "Applicant", "notes": "Each cost must be directly tied to eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II–IV", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1f89af81-b13c-4639-a7f3-2b1d6f27e5bf", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Applicant used their own employees for response or repairs", "action_required_then": "Track hours, activities, pay rates for each employee by project", "grok_tag": "GROK.General", "step/requirement": "Document Force Account Labor (FAL)", "documentation_required": "Timesheets, payroll records, labor policies, fringe benefit breakdowns", "responsible_party": "Applicant", "notes": "Track emergency vs. permanent work separately", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "04ba71e3-1b0c-491e-8103-d3971c37474b", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Applicant used owned or leased equipment", "action_required_then": "Log hours, type, operator, and task for each equipment use", "grok_tag": "GROK.General", "step/requirement": "Document Force Account Equipment (FAE)", "documentation_required": "Daily usage logs, FEMA equipment rate schedules, operator assignments", "responsible_party": "Applicant", "notes": "Use FEMA or state-approved equipment rates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "3d029fab-4420-430f-a096-9a5807aa92bf", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Work performed by contracts or vendors", "action_required_then": "Comply with 2 CFR §200 procurement standards", "grok_tag": "GROK.General", "step/requirement": "Ensure Procurement Compliance", "documentation_required": "Bid documents, RFPs, contracts, scoring sheets, justifications", "responsible_party": "Applicant", "notes": "Use competitive procurement unless exceptions apply", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § VI; 2 CFR §200.317–327", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a3baf7a2-a96a-400a-88ba-4732a623db54", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Materials were purchased or used from inventory", "action_required_then": "Provide quantity, unit price, source, and use case", "grok_tag": "GROK.General", "step/requirement": "Track Material and Supply Costs", "documentation_required": "Receipts, inventory logs, invoices, delivery tickets", "responsible_party": "Applicant", "notes": "Only materials used for eligible work are reimbursable", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.3", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "600dbcd0-f1bd-49ec-948a-35e15432becd", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "External public or volunteer resources were used", "action_required_then": "Document hours, type of service, agreements or offers of assistance", "grok_tag": "GROK.General", "step/requirement": "Account for Mutual Aid and Volunteer Labor", "documentation_required": "Mutual aid agreements, volunteer sign-in sheets, service logs", "responsible_party": "Applicant", "notes": "Labor must be tracked and assigned per project", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.C", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "035e776d-34d4-4695-a401-e8eea81bf3a2", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "All cost data is collected", "action_required_then": "Assess cost reasonableness compared to market rates", "grok_tag": "GROK.General", "step/requirement": "Verify Cost Reasonableness", "documentation_required": "Comparative cost analyses, FEMA unit cost guides, vendor quotes", "responsible_party": "FEMA and Applicant", "notes": "Unreasonable costs are subject to reduction", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P4.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II.E", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d8e7d7de-526e-4edb-9b2c-4f12a6cff46f", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Work has been completed or is ongoing", "action_required_then": "Categorize expenses: labor, equipment, materials, contracts, donations, volunteers", "grok_tag": "GROK.General", "step/requirement": "Identify All Cost Types (Force Account, Contract, Materials)", "documentation_required": "Timesheets, equipment logs, invoices, procurement records", "responsible_party": "Applicant", "notes": "Each cost must be directly tied to eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II–IV", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "f19e8afe-6983-45cc-94df-42ebe78cb3a2", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Applicant used their own employees for response or repairs", "action_required_then": "Track hours, activities, pay rates for each employee by project", "grok_tag": "GROK.General", "step/requirement": "Document Force Account Labor (FAL)", "documentation_required": "Timesheets, payroll records, labor policies, fringe benefit breakdowns", "responsible_party": "Applicant", "notes": "Track emergency vs. permanent work separately", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "48b03a43-1958-4734-8f99-aba3695575b7", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Applicant used owned or leased equipment", "action_required_then": "Log hours, type, operator, and task for each equipment use", "grok_tag": "GROK.General", "step/requirement": "Document Force Account Equipment (FAE)", "documentation_required": "Daily usage logs, FEMA equipment rate schedules, operator assignments", "responsible_party": "Applicant", "notes": "Use FEMA or state-approved equipment rates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1d4ffe1d-539e-4b4c-ab2e-0e2bbd47ce6d", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Work performed by contracts or vendors", "action_required_then": "Comply with 2 CFR §200 procurement standards", "grok_tag": "GROK.General", "step/requirement": "Ensure Procurement Compliance", "documentation_required": "Bid documents, RFPs, contracts, scoring sheets, justifications", "responsible_party": "Applicant", "notes": "Use competitive procurement unless exceptions apply", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § VI; 2 CFR §200.317–327", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "5f475a6f-348c-47e9-8fb7-e54b4eb969b1", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Materials were purchased or used from inventory", "action_required_then": "Provide quantity, unit price, source, and use case", "grok_tag": "GROK.General", "step/requirement": "Track Material and Supply Costs", "documentation_required": "Receipts, inventory logs, invoices, delivery tickets", "responsible_party": "Applicant", "notes": "Only materials used for eligible work are reimbursable", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.3", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "2d6ba2ba-7dca-466d-a65f-947275734fa5", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "External public or volunteer resources were used", "action_required_then": "Document hours, type of service, agreements or offers of assistance", "grok_tag": "GROK.General", "step/requirement": "Account for Mutual Aid and Volunteer Labor", "documentation_required": "Mutual aid agreements, volunteer sign-in sheets, service logs", "responsible_party": "Applicant", "notes": "Labor must be tracked and assigned per project", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.C", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "e6c844f7-1f8b-4318-8af4-a8140e11bfb4", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "All cost data is collected", "action_required_then": "Assess cost reasonableness compared to market rates", "grok_tag": "GROK.General", "step/requirement": "Verify Cost Reasonableness", "documentation_required": "Comparative cost analyses, FEMA unit cost guides, vendor quotes", "responsible_party": "FEMA and Applicant", "notes": "Unreasonable costs are subject to reduction", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II.E", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "6d70a9da-514f-49d3-9bce-1282c6597a8a", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Work has been completed or is ongoing", "action_required_then": "Categorize expenses: labor, equipment, materials, contracts, donations, volunteers", "grok_tag": "GROK.General", "step/requirement": "Identify All Cost Types (Force Account, Contract, Materials)", "documentation_required": "Timesheets, equipment logs, invoices, procurement records", "responsible_party": "Applicant", "notes": "Each cost must be directly tied to eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II–IV", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "6b177d36-1edf-41e7-8613-a8c6226bd45d", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Applicant used their own employees for response or repairs", "action_required_then": "Track hours, activities, pay rates for each employee by project", "grok_tag": "GROK.General", "step/requirement": "Document Force Account Labor (FAL)", "documentation_required": "Timesheets, payroll records, labor policies, fringe benefit breakdowns", "responsible_party": "Applicant", "notes": "Track emergency vs. permanent work separately", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "61eb4486-3c1c-4080-ad69-8ca61d05d89d", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Applicant used owned or leased equipment", "action_required_then": "Log hours, type, operator, and task for each equipment use", "grok_tag": "GROK.General", "step/requirement": "Document Force Account Equipment (FAE)", "documentation_required": "Daily usage logs, FEMA equipment rate schedules, operator assignments", "responsible_party": "Applicant", "notes": "Use FEMA or state-approved equipment rates", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.2", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "f1d4e142-864e-4b9f-9a57-544ef209b9d7", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Work performed by contracts or vendors", "action_required_then": "Comply with 2 CFR §200 procurement standards", "grok_tag": "GROK.General", "step/requirement": "Ensure Procurement Compliance", "documentation_required": "Bid documents, RFPs, contracts, scoring sheets, justifications", "responsible_party": "Applicant", "notes": "Use competitive procurement unless exceptions apply", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § VI; 2 CFR §200.317–327", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "2126934e-33aa-45db-954b-adbbd0ba8ab3", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "Materials were purchased or used from inventory", "action_required_then": "Provide quantity, unit price, source, and use case", "grok_tag": "GROK.General", "step/requirement": "Track Material and Supply Costs", "documentation_required": "Receipts, inventory logs, invoices, delivery tickets", "responsible_party": "Applicant", "notes": "Only materials used for eligible work are reimbursable", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.B.3", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1ed6445a-b18a-4d0f-bf50-fca95cfa6fdc", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "External public or volunteer resources were used", "action_required_then": "Document hours, type of service, agreements or offers of assistance", "grok_tag": "GROK.General", "step/requirement": "Account for Mutual Aid and Volunteer Labor", "documentation_required": "Mutual aid agreements, volunteer sign-in sheets, service logs", "responsible_party": "Applicant", "notes": "Labor must be tracked and assigned per project", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § IV.C", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "0c072fc5-190d-4da8-990e-37ca89176787", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility & Procurement", "trigger_condition_if": "All cost data is collected", "action_required_then": "Assess cost reasonableness compared to market rates", "grok_tag": "GROK.General", "step/requirement": "Verify Cost Reasonableness", "documentation_required": "Comparative cost analyses, FEMA unit cost guides, vendor quotes", "responsible_party": "FEMA and Applicant", "notes": "Unreasonable costs are subject to reduction", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 9 § II.E", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "167a2ddc-78d2-49c5-a3b8-624163dea22a", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Applicant submits project costs", "action_required_then": "Demonstrate costs are necessary and reasonable for the work performed", "grok_tag": "GROK.General", "step/requirement": "Establish Reasonable Cost", "documentation_required": "Cost comparisons, historical pricing, procurement records, quotes", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § II.A", "notes": "FEMA may require a reasonableness analysis", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "4a60f579-f96e-4553-bf46-259f1cf5caf3", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Labor costs are claimed", "action_required_then": "Verify labor was disaster-related and within policy rules", "grok_tag": "GROK.General", "step/requirement": "Applicant Labor Eligibility", "documentation_required": "Timesheets, pay policies, job descriptions, disaster assignments", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, Table 10 & 11", "notes": "Differentiation required for emergency vs permanent work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "682ada18-b1a6-4202-9155-ca598d80f912", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Contracted work is used", "action_required_then": "Follow federal procurement rules based on entity type", "grok_tag": "GROK.General", "step/requirement": "Procurement Method Compliance", "documentation_required": "RFPs, bids, contracts, evaluation scoresheets, cost reasonableness", "responsible_party": "Applicant", "applicable_regulations": "2 C.F.R. §§ 200.318–327; PAPPG v5.0, Chapter 6, § X", "notes": "Include time-and-material justification if used", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "efcbac80-2baa-49f9-a73d-f4557d5d6060", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Applicant uses support from another jurisdiction", "action_required_then": "Document mutual aid agreements and actual costs incurred", "grok_tag": "GROK.General", "step/requirement": "Mutual Aid Agreements", "documentation_required": "MOUs/MOAs, deployment logs, invoices, cost summaries", "responsible_party": "Applicant and partner entity", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XI", "notes": "Agreements can be pre- or post-incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "04e40d13-74b5-42a5-9bcb-e5d4720d937f", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Volunteer labor or materials are used", "action_required_then": "Track and document donated services for offset credit", "grok_tag": "GROK.General", "step/requirement": "Validate Donated Resources", "documentation_required": "Timesheets, material logs, fair market valuation", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XVI, Table 18", "notes": "Must directly support eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase4.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "23b8eb96-be8d-4400-ae7f-b975b598fce6", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Applicant submits project costs", "action_required_then": "Demonstrate costs are necessary and reasonable for the work performed", "grok_tag": "GROK.General", "step/requirement": "Establish Reasonable Cost", "documentation_required": "Cost comparisons, historical pricing, procurement records, quotes", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § II.A", "notes": "FEMA may require a reasonableness analysis", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "fa13b797-23e9-4352-a7b8-628bc69d607f", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Labor costs are claimed", "action_required_then": "Verify labor was disaster-related and within policy rules", "grok_tag": "GROK.General", "step/requirement": "Applicant Labor Eligibility", "documentation_required": "Timesheets, pay policies, job descriptions, disaster assignments", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, Table 10 & 11", "notes": "Differentiation required for emergency vs permanent work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "d3f85a1b-d59a-423a-a742-e768fd7a8def", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Contracted work is used", "action_required_then": "Follow federal procurement rules based on entity type", "grok_tag": "GROK.General", "step/requirement": "Procurement Method Compliance", "documentation_required": "RFPs, bids, contracts, evaluation scoresheets, cost reasonableness", "responsible_party": "Applicant", "applicable_regulations": "2 C.F.R. §§ 200.318–327; PAPPG v5.0, Chapter 6, § X", "notes": "Include time-and-material justification if used", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "9c08438c-8626-47c4-b344-58027f208f65", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Applicant uses support from another jurisdiction", "action_required_then": "Document mutual aid agreements and actual costs incurred", "grok_tag": "GROK.General", "step/requirement": "Mutual Aid Agreements", "documentation_required": "MOUs/MOAs, deployment logs, invoices, cost summaries", "responsible_party": "Applicant and partner entity", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XI", "notes": "Agreements can be pre- or post-incident", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "951e6db2-ba68-4a5f-91a5-bfa995406eff", "grokTags": ["GROK.General"]}, {"phase": 4, "process_phase": "Phase 4: Cost Eligibility and Procurement", "trigger_condition_if": "Volunteer labor or materials are used", "action_required_then": "Track and document donated services for offset credit", "grok_tag": "GROK.General", "step/requirement": "Validate Donated Resources", "documentation_required": "Timesheets, material logs, fair market valuation", "responsible_party": "Applicant", "applicable_regulations": "PAPPG v5.0, Chapter 6, § XVI, Table 18", "notes": "Must directly support eligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "23b9f2c8-97d5-4341-8215-27507d1c3012", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Damage Inventory and cost documentation are complete", "action_required_then": "Begin formulation of individual Project Worksheets (PWs)", "grok_tag": "GROK.General", "step/requirement": "Initiate Project Formulation", "documentation_required": "DI, RSM notes, cost documentation, site photos, insurance info", "responsible_party": "Applicant and PDMG", "notes": "Projects may be grouped or separated by type, site, or funding stream", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § I–II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "8ad9e0be-b6ea-49c8-b4f0-11104278edce", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Project formulation is underway", "action_required_then": "Describe eligible work in detail, including methods, materials, timeline", "grok_tag": "GROK.General", "step/requirement": "Define Scope of Work (SOW)", "documentation_required": "Engineer estimates, photos, site maps, damage descriptions", "responsible_party": "Applicant, reviewed by FEMA", "notes": "SOW must match eligible damages and clearly exclude ineligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "28e7f797-6149-4ae6-9604-a625d86a5d71", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Permanent work involves repair or replacement", "action_required_then": "Ensure design follows latest published and appropriate CBCS", "grok_tag": "GROK.General", "step/requirement": "Apply Consensus-Based Codes and Standards (CBCS)", "documentation_required": "Building codes, engineering specs, code citations, local ordinances", "responsible_party": "Applicant and design professionals", "notes": "Must be uniformly enforced and apply to all similar facilities", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 7 § II.A.1; Stafford Act §406(e)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "c0ab7205-5997-42ef-961c-9085f2e20288", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Project involves ground disturbance, historic properties, or protected areas", "action_required_then": "Complete FEMA EHP review before obligation of funds", "grok_tag": "GROK.General", "step/requirement": "Perform Environmental and Historic Preservation (EHP) Review", "documentation_required": "Maps, permits, consultation letters (SHPO, USFWS, EPA), NEPA documentation", "responsible_party": "FEMA EHP with Applicant support", "notes": "Must identify all environmental and cultural impacts", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA, NHPA, ESA, 44 CFR Part 9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "82d02671-0acd-40ad-9a6f-3c6af61c5527", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Permanent work allows opportunity to reduce future risk", "action_required_then": "Integrate mitigation measures into SOW and estimate", "grok_tag": "GROK.General", "step/requirement": "Identify Section 406 Hazard Mitigation Proposals", "documentation_required": "Risk analysis, benefit-cost data, engineering justifications", "responsible_party": "Applicant and PDMG", "notes": "Mitigation must be cost-effective and technically feasible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406(c)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "2840c6e8-f93e-4f75-93b2-86df0901ab9f", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "SOW is defined", "action_required_then": "Prepare cost estimate using approved methods (e.g., cost codes, RSMeans)", "grok_tag": "GROK.General", "step/requirement": "Develop Cost Estimate", "documentation_required": "Line-item cost breakdowns, quotes, cost guides", "responsible_party": "Applicant, reviewed by FEMA", "notes": "FEMA may validate estimate using their Cost Estimating Format (CEF)", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P5.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 6 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "90ebda87-e599-42b0-b655-81abc32be3e5", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Damage Inventory and cost documentation are complete", "action_required_then": "Begin formulation of individual Project Worksheets (PWs)", "grok_tag": "GROK.General", "step/requirement": "Initiate Project Formulation", "documentation_required": "DI, RSM notes, cost documentation, site photos, insurance info", "responsible_party": "Applicant and PDMG", "notes": "Projects may be grouped or separated by type, site, or funding stream", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § I–II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "466335ff-26c6-459c-b186-cc4608fb2597", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Project formulation is underway", "action_required_then": "Describe eligible work in detail, including methods, materials, timeline", "grok_tag": "GROK.General", "step/requirement": "Define Scope of Work (SOW)", "documentation_required": "Engineer estimates, photos, site maps, damage descriptions", "responsible_party": "Applicant, reviewed by FEMA", "notes": "SOW must match eligible damages and clearly exclude ineligible work", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "988f455a-97ea-4230-acd6-fb3eb2b48305", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Permanent work involves repair or replacement", "action_required_then": "Ensure design follows latest published and appropriate CBCS", "grok_tag": "GROK.General", "step/requirement": "Apply Consensus-Based Codes and Standards (CBCS)", "documentation_required": "Building codes, engineering specs, code citations, local ordinances", "responsible_party": "Applicant and design professionals", "notes": "Must be uniformly enforced and apply to all similar facilities", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 7 § II.A.1; Stafford Act §406(e)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "253ddb67-afb1-4b3d-be65-fd1524f51ac0", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Project involves ground disturbance, historic properties, or protected areas", "action_required_then": "Complete FEMA EHP review before obligation of funds", "grok_tag": "GROK.General", "step/requirement": "Perform Environmental and Historic Preservation (EHP) Review", "documentation_required": "Maps, permits, consultation letters (SHPO, USFWS, EPA), NEPA documentation", "responsible_party": "FEMA EHP with Applicant support", "notes": "Must identify all environmental and cultural impacts", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 10; NEPA, NHPA, ESA, 44 CFR Part 9", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "019a716e-39a7-48e0-82d9-674f48307758", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "Permanent work allows opportunity to reduce future risk", "action_required_then": "Integrate mitigation measures into SOW and estimate", "grok_tag": "GROK.General", "step/requirement": "Identify Section 406 Hazard Mitigation Proposals", "documentation_required": "Risk analysis, benefit-cost data, engineering justifications", "responsible_party": "Applicant and PDMG", "notes": "Mitigation must be cost-effective and technically feasible", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 8 § II; Stafford Act §406(c)", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "692f131e-556b-47cb-960c-4c89b0fb3b8d", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scope", "trigger_condition_if": "SOW is defined", "action_required_then": "Prepare cost estimate using approved methods (e.g., cost codes, RSMeans)", "grok_tag": "GROK.General", "step/requirement": "Develop Cost Estimate", "documentation_required": "Line-item cost breakdowns, quotes, cost guides", "responsible_party": "Applicant, reviewed by FEMA", "notes": "FEMA may validate estimate using their Cost Estimating Format (CEF)", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 6 § II", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "1bcb7da4-693d-4840-a142-f39ae6a917c9", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scoping", "trigger_condition_if": "Damage is confirmed and costs identified", "action_required_then": "Draft project scope with detailed work description and quantifiable costs", "grok_tag": "GROK.General", "step/requirement": "<PERSON><PERSON><PERSON>ope of Work (SOW)", "documentation_required": "Damage descriptions, SOW template, engineering reports, cost data", "responsible_party": "Applicant, with FEMA and Recipient review", "applicable_regulations": "PAPPG v5.0, Chapter 9, § I", "notes": "Scope must reflect eligible work only", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "7c11c8e8-18f5-4006-8c51-b6b95279719d", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scoping", "trigger_condition_if": "Project cost is estimated", "action_required_then": "Classify as small or large project based on FEMA thresholds", "grok_tag": "GROK.General", "step/requirement": "Project Threshold Classification", "documentation_required": "Estimated cost worksheets, FEMA thresholds table", "responsible_party": "FEMA and Recipient", "applicable_regulations": "PAPPG v5.0, Chapter 9, § II.A", "notes": "Threshold updated annually", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "47fa53ba-8a64-45f1-960b-4c811a4cfcba", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scoping", "trigger_condition_if": "Costs are submitted with scope", "action_required_then": "FEMA evaluates cost reasonableness for each line item", "grok_tag": "GROK.General", "step/requirement": "Conduct Cost Reasonableness Review", "documentation_required": "Engineer estimates, historical pricing, procurement docs", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 9, § II.D–E", "notes": "Can use FEMA cost estimating tools", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "a61c0013-559e-4527-9759-f4d8475f8f58", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scoping", "trigger_condition_if": "Work affects environment or historic structures", "action_required_then": "Submit required documentation for FEMA EHP review", "grok_tag": "GROK.General", "step/requirement": "Environmental & Historic Preservation (EHP) Review", "documentation_required": "Maps, site photos, EHP forms, coordination records", "responsible_party": "Applicant and FEMA EHP staff", "applicable_regulations": "PAPPG v5.0, Chapter 10, § I–II", "notes": "Delays occur without complete EHP data", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "eac275dd-052a-42e2-a5b8-2cab58aa2eb1", "grokTags": ["GROK.General"]}, {"phase": 5, "process_phase": "Phase 5: Project Formulation & Scoping", "trigger_condition_if": "Project is approved and passes compliance review", "action_required_then": "FEMA obligates funding through Grants Manager", "grok_tag": "GROK.General", "step/requirement": "FEMA Obligation of Funds", "documentation_required": "Final scope and cost, approval memos, compliance clearance", "responsible_party": "FEMA", "applicable_regulations": "PAPPG v5.0, Chapter 9, § IV", "notes": "Obligation date starts funding and reporting timelines", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_Phase5.xlsx", "sheetname": "Sheet1", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "5072dd1f-026c-4ab2-bcc5-b5db4314dd06", "grokTags": ["GROK.General"]}, {"phase": 6, "process_phase": "Phase 6: Project Review & Obligation", "trigger_condition_if": "Project Worksheet is finalized", "action_required_then": "FEMA conducts eligibility, cost, and documentation compliance review", "grok_tag": "GROK.General", "step/requirement": "Conduct Compliance Review", "documentation_required": "Complete PW package, policy citations, supporting documents", "responsible_party": "FEMA and Recipient", "notes": "Projects reviewed for duplication of benefits, cost reasonableness, and eligibility", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § III", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "f3cfd8f0-db12-4f1e-92ab-e3ad83ef4099", "grokTags": ["GROK.General"]}, {"phase": 6, "process_phase": "Phase 6: Project Review & Obligation", "trigger_condition_if": "Insurance, grants, or other funding also apply to damages", "action_required_then": "Ensure PA funding does not duplicate other sources", "grok_tag": "GROK.General", "step/requirement": "Check for Duplication of Benefits (DOB)", "documentation_required": "Insurance settlement statements, SBA, HUD, or other grant award letters", "responsible_party": "Applicant and FEMA", "notes": "DOB reductions are mandatory before obligation", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § V.C; Stafford Act §312", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "c46e8143-48b8-457a-992c-bc95e3ae8985", "grokTags": ["GROK.General"]}, {"phase": 6, "process_phase": "Phase 6: Project Review & Obligation", "trigger_condition_if": "Insurable facilities receive insurance payments or are covered by policy", "action_required_then": "Subtract actual or anticipated insurance proceeds from eligible project costs", "grok_tag": "GROK.General", "step/requirement": "Apply Insurance Reductions", "documentation_required": "Insurance policies, claims, correspondence with carriers", "responsible_party": "Applicant and FEMA", "notes": "NFIP compliance is mandatory for flood-damaged properties", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 3 § V.C; 44 CFR §206.252–253", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "dd4dafee-7ac2-4a0a-a2d7-b7856e630585", "grokTags": ["GROK.General"]}, {"phase": 6, "process_phase": "Phase 6: Project Review & Obligation", "trigger_condition_if": "All project review steps are complete", "action_required_then": "Submit PW for final Recipient review and FEMA obligation", "grok_tag": "GROK.General", "step/requirement": "Finalize Project Worksheet (PW)", "documentation_required": "Finalized PW, approval signatures, version control", "responsible_party": "Applicant, Recipient, and FEMA", "notes": "FEMA issues obligation letter once PW is approved", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § IV", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "11e526e8-b7ce-4b6a-92d2-d0e657011f53", "grokTags": ["GROK.General"]}, {"phase": 6, "process_phase": "Phase 6: Project Review & Obligation", "trigger_condition_if": "FEMA approves and obligates funds", "action_required_then": "Recipient notifies Applicant of approved amount", "grok_tag": "GROK.General", "step/requirement": "Project Obligation Notification", "documentation_required": "Obligation letter, project summary, fund release notice", "responsible_party": "FEMA and Recipient", "notes": "Starts the clock for project deadlines and closeout", "sourcefile": "FEMA_PA_ComplianceMax_Checklist_P1_to_P6.xlsx", "sheetname": "Sheet1", "applicable_regulations_or_pappg_reference": "PAPPG Ch. 5 § IV", "checklist_damageinventory": 0, "checklist_damagedescription": 0, "checklist_costing": 0, "checklist_invoices": 0, "checklist_mitigation": 0, "checklist_ehp": 0, "checklist_insurance": 0, "checklist_labordocs": 0, "checklist_contracts": 0, "checklist_debrisdocs": 0, "checklist_progressreports": 0, "checklist_closeout": 0, "executedcontract_checked": 0, "procurementprocedure_checked": 0, "solicitation_checked": 0, "bid_checked": 0, "evaluation_checked": 0, "invoice_checked": 0, "checkbox": 0, "sourcesheet": "DOCS PROCESS REQUIREMEMENTS", "doctyperequired_checkbox": false, "action_required_checkbox": false, "condition_checkbox": false, "incident_date_simulated": "2016-06-15", "pappg_version_auto": "PAPPG v1.0", "id": "0bca161a-85f1-4a85-95e2-500698796583", "grokTags": ["GROK.General"]}]