import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { v4 as uuidv4 } from 'uuid';
import { 
  WorkflowDefinition, 
  WorkflowNode, 
  WorkflowEdge, 
  WorkflowDesignerState,
  ValidationError,
  Collaborator,
  WorkflowNodeType,
  FEMAProgram,
  WorkflowCategory,
  WorkflowStatus
} from '../types/workflow';

interface WorkflowStoreState extends WorkflowDesignerState {
  // Additional UI state
  isLoading: boolean;
  isSaving: boolean;
  isValidating: boolean;
  sidebarOpen: boolean;
  activeTab: 'properties' | 'variables' | 'rules' | 'history';
  zoomLevel: number;
  
  // Actions
  setWorkflowDefinition: (workflow: WorkflowDefinition) => void;
  updateWorkflowMetadata: (metadata: Partial<WorkflowDefinition>) => void;
  
  // Node operations
  addNode: (nodeType: WorkflowNodeType, position: { x: number; y: number }) => string;
  updateNode: (nodeId: string, updates: Partial<WorkflowNode>) => void;
  deleteNode: (nodeId: string) => void;
  duplicateNode: (nodeId: string) => string;
  moveNode: (nodeId: string, position: { x: number; y: number }) => void;
  selectNode: (nodeId: string | null) => void;
  
  // Edge operations
  addEdge: (source: string, target: string) => string;
  updateEdge: (edgeId: string, updates: Partial<WorkflowEdge>) => void;
  deleteEdge: (edgeId: string) => void;
  selectEdge: (edgeId: string | null) => void;
  
  // Clipboard operations
  copySelection: () => void;
  pasteSelection: (position?: { x: number; y: number }) => void;
  cutSelection: () => void;
  
  // History operations
  undo: () => void;
  redo: () => void;
  addToHistory: () => void;
  clearHistory: () => void;
  
  // Validation
  validateWorkflow: () => ValidationError[];
  clearValidationErrors: () => void;
  
  // Collaboration
  addCollaborator: (collaborator: Collaborator) => void;
  removeCollaborator: (collaboratorId: string) => void;
  updateCollaboratorCursor: (collaboratorId: string, cursor: { x: number; y: number }) => void;
  
  // UI state
  setSidebarOpen: (open: boolean) => void;
  setActiveTab: (tab: 'properties' | 'variables' | 'rules' | 'history') => void;
  setZoomLevel: (level: number) => void;
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  
  // Workflow operations
  saveWorkflow: () => Promise<void>;
  loadWorkflow: (workflowId: string) => Promise<void>;
  exportWorkflow: () => string;
  importWorkflow: (data: string) => void;
  
  // Reset store
  resetStore: () => void;
}

const createEmptyWorkflow = (): WorkflowDefinition => ({
  id: uuidv4(),
  name: 'New Workflow',
  description: '',
  version: '1.0.0',
  category: 'FEMA_COMPLIANCE' as WorkflowCategory,
  femaProgram: 'PUBLIC_ASSISTANCE' as FEMAProgram,
  status: 'DRAFT' as WorkflowStatus,
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'current-user', // This would come from auth context
  tags: [],
  metadata: {},
  nodes: [],
  edges: [],
  startNodeId: '',
  variables: [],
  rules: [],
  permissions: {
    view: ['authenticated'],
    edit: ['admin', 'editor'],
    execute: ['authenticated'],
    admin: ['admin']
  }
});

const createNode = (
  type: WorkflowNodeType, 
  position: { x: number; y: number }
): WorkflowNode => {
  const id = uuidv4();
  
  const baseNode: WorkflowNode = {
    id,
    type,
    position,
    data: {
      label: getDefaultNodeLabel(type),
      description: '',
      config: {},
      validations: [],
      assignments: [],
      notifications: [],
      integrations: [],
      customFields: []
    }
  };

  // Add FEMA-specific configuration for FEMA node types
  if (type.startsWith('fema-')) {
    baseNode.data.femaConfig = {
      program: 'PUBLIC_ASSISTANCE',
      eligibilityRules: [],
      costCalculations: [],
      requiredDocuments: [],
      reviewCriteria: [],
      complianceChecks: []
    };
  }

  return baseNode;
};

const getDefaultNodeLabel = (type: WorkflowNodeType): string => {
  const labels: Record<WorkflowNodeType, string> = {
    'start': 'Start',
    'end': 'End',
    'task': 'Task',
    'approval': 'Approval',
    'decision': 'Decision',
    'document': 'Document',
    'calculation': 'Calculation',
    'integration': 'Integration',
    'notification': 'Notification',
    'timer': 'Timer',
    'parallel': 'Parallel Gateway',
    'merge': 'Merge Gateway',
    'fema-eligibility': 'FEMA Eligibility Check',
    'fema-cost-calc': 'FEMA Cost Calculation',
    'fema-review': 'FEMA Review',
    'fema-submission': 'FEMA Submission'
  };
  
  return labels[type] || 'Unknown Node';
};

const validateWorkflowDefinition = (workflow: WorkflowDefinition): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  // Basic validation
  if (!workflow.name.trim()) {
    errors.push({
      id: uuidv4(),
      type: 'ERROR',
      message: 'Workflow name is required',
      field: 'name'
    });
  }
  
  // Node validation
  if (workflow.nodes.length === 0) {
    errors.push({
      id: uuidv4(),
      type: 'WARNING',
      message: 'Workflow has no nodes'
    });
  }
  
  // Start node validation
  const startNodes = workflow.nodes.filter(node => node.type === 'start');
  if (startNodes.length === 0) {
    errors.push({
      id: uuidv4(),
      type: 'ERROR',
      message: 'Workflow must have a start node'
    });
  } else if (startNodes.length > 1) {
    errors.push({
      id: uuidv4(),
      type: 'ERROR',
      message: 'Workflow can only have one start node'
    });
  }
  
  // End node validation
  const endNodes = workflow.nodes.filter(node => node.type === 'end');
  if (endNodes.length === 0) {
    errors.push({
      id: uuidv4(),
      type: 'WARNING',
      message: 'Workflow should have at least one end node'
    });
  }
  
  // Edge validation
  workflow.nodes.forEach(node => {
    if (node.type !== 'end') {
      const outgoingEdges = workflow.edges.filter(edge => edge.source === node.id);
      if (outgoingEdges.length === 0) {
        errors.push({
          id: uuidv4(),
          type: 'WARNING',
          message: `Node "${node.data.label}" has no outgoing connections`,
          nodeId: node.id
        });
      }
    }
    
    if (node.type !== 'start') {
      const incomingEdges = workflow.edges.filter(edge => edge.target === node.id);
      if (incomingEdges.length === 0) {
        errors.push({
          id: uuidv4(),
          type: 'WARNING',
          message: `Node "${node.data.label}" has no incoming connections`,
          nodeId: node.id
        });
      }
    }
  });
  
  return errors;
};

export const useWorkflowStore = create<WorkflowStoreState>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // Initial state
        workflowDefinition: createEmptyWorkflow(),
        selectedNode: undefined,
        selectedEdge: undefined,
        isEditing: false,
        isDirty: false,
        history: [],
        historyIndex: -1,
        clipboard: undefined,
        validationErrors: [],
        collaborators: [],
        
        // UI state
        isLoading: false,
        isSaving: false,
        isValidating: false,
        sidebarOpen: true,
        activeTab: 'properties',
        zoomLevel: 1,
        
        // Workflow operations
        setWorkflowDefinition: (workflow) => set((state) => {
          state.workflowDefinition = workflow;
          state.isDirty = false;
          state.addToHistory();
        }),
        
        updateWorkflowMetadata: (metadata) => set((state) => {
          Object.assign(state.workflowDefinition, metadata);
          state.workflowDefinition.updatedAt = new Date();
          state.isDirty = true;
        }),
        
        // Node operations
        addNode: (nodeType, position) => {
          const nodeId = uuidv4();
          set((state) => {
            const newNode = createNode(nodeType, position);
            newNode.id = nodeId;
            state.workflowDefinition.nodes.push(newNode);
            state.selectedNode = newNode;
            state.selectedEdge = undefined;
            state.isDirty = true;
            state.addToHistory();
          });
          return nodeId;
        },
        
        updateNode: (nodeId, updates) => set((state) => {
          const nodeIndex = state.workflowDefinition.nodes.findIndex(n => n.id === nodeId);
          if (nodeIndex !== -1) {
            Object.assign(state.workflowDefinition.nodes[nodeIndex], updates);
            state.isDirty = true;
          }
        }),
        
        deleteNode: (nodeId) => set((state) => {
          // Remove node
          state.workflowDefinition.nodes = state.workflowDefinition.nodes.filter(n => n.id !== nodeId);
          
          // Remove connected edges
          state.workflowDefinition.edges = state.workflowDefinition.edges.filter(
            e => e.source !== nodeId && e.target !== nodeId
          );
          
          // Clear selection if deleted node was selected
          if (state.selectedNode?.id === nodeId) {
            state.selectedNode = undefined;
          }
          
          state.isDirty = true;
          state.addToHistory();
        }),
        
        duplicateNode: (nodeId) => {
          const newNodeId = uuidv4();
          set((state) => {
            const originalNode = state.workflowDefinition.nodes.find(n => n.id === nodeId);
            if (originalNode) {
              const duplicatedNode: WorkflowNode = {
                ...originalNode,
                id: newNodeId,
                position: {
                  x: originalNode.position.x + 50,
                  y: originalNode.position.y + 50
                },
                data: {
                  ...originalNode.data,
                  label: `${originalNode.data.label} (Copy)`
                }
              };
              state.workflowDefinition.nodes.push(duplicatedNode);
              state.selectedNode = duplicatedNode;
              state.isDirty = true;
              state.addToHistory();
            }
          });
          return newNodeId;
        },
        
        moveNode: (nodeId, position) => set((state) => {
          const node = state.workflowDefinition.nodes.find(n => n.id === nodeId);
          if (node) {
            node.position = position;
            state.isDirty = true;
          }
        }),
        
        selectNode: (nodeId) => set((state) => {
          state.selectedNode = nodeId ? state.workflowDefinition.nodes.find(n => n.id === nodeId) : undefined;
          state.selectedEdge = undefined;
        }),
        
        // Edge operations
        addEdge: (source, target) => {
          const edgeId = uuidv4();
          set((state) => {
            const newEdge: WorkflowEdge = {
              id: edgeId,
              source,
              target,
              data: {
                conditions: [],
                priority: 0
              }
            };
            state.workflowDefinition.edges.push(newEdge);
            state.selectedEdge = newEdge;
            state.selectedNode = undefined;
            state.isDirty = true;
            state.addToHistory();
          });
          return edgeId;
        },
        
        updateEdge: (edgeId, updates) => set((state) => {
          const edgeIndex = state.workflowDefinition.edges.findIndex(e => e.id === edgeId);
          if (edgeIndex !== -1) {
            Object.assign(state.workflowDefinition.edges[edgeIndex], updates);
            state.isDirty = true;
          }
        }),
        
        deleteEdge: (edgeId) => set((state) => {
          state.workflowDefinition.edges = state.workflowDefinition.edges.filter(e => e.id !== edgeId);
          if (state.selectedEdge?.id === edgeId) {
            state.selectedEdge = undefined;
          }
          state.isDirty = true;
          state.addToHistory();
        }),
        
        selectEdge: (edgeId) => set((state) => {
          state.selectedEdge = edgeId ? state.workflowDefinition.edges.find(e => e.id === edgeId) : undefined;
          state.selectedNode = undefined;
        }),
        
        // Clipboard operations
        copySelection: () => set((state) => {
          if (state.selectedNode) {
            state.clipboard = {
              type: 'NODE',
              data: { ...state.selectedNode }
            };
          } else if (state.selectedEdge) {
            state.clipboard = {
              type: 'EDGE',
              data: { ...state.selectedEdge }
            };
          }
        }),
        
        pasteSelection: (position = { x: 100, y: 100 }) => set((state) => {
          if (state.clipboard?.type === 'NODE' && state.clipboard.data) {
            const newNode: WorkflowNode = {
              ...state.clipboard.data,
              id: uuidv4(),
              position,
              data: {
                ...state.clipboard.data.data,
                label: `${state.clipboard.data.data.label} (Copy)`
              }
            };
            state.workflowDefinition.nodes.push(newNode);
            state.selectedNode = newNode;
            state.isDirty = true;
            state.addToHistory();
          }
        }),
        
        cutSelection: () => set((state) => {
          if (state.selectedNode) {
            state.clipboard = {
              type: 'NODE',
              data: { ...state.selectedNode }
            };
            get().deleteNode(state.selectedNode.id);
          } else if (state.selectedEdge) {
            state.clipboard = {
              type: 'EDGE',
              data: { ...state.selectedEdge }
            };
            get().deleteEdge(state.selectedEdge.id);
          }
        }),
        
        // History operations
        addToHistory: () => set((state) => {
          // Remove any future history if we're not at the end
          if (state.historyIndex < state.history.length - 1) {
            state.history = state.history.slice(0, state.historyIndex + 1);
          }
          
          // Add current state to history
          state.history.push(JSON.parse(JSON.stringify(state.workflowDefinition)));
          state.historyIndex = state.history.length - 1;
          
          // Limit history size
          if (state.history.length > 50) {
            state.history = state.history.slice(-50);
            state.historyIndex = state.history.length - 1;
          }
        }),
        
        undo: () => set((state) => {
          if (state.historyIndex > 0) {
            state.historyIndex--;
            state.workflowDefinition = JSON.parse(JSON.stringify(state.history[state.historyIndex]));
            state.selectedNode = undefined;
            state.selectedEdge = undefined;
            state.isDirty = true;
          }
        }),
        
        redo: () => set((state) => {
          if (state.historyIndex < state.history.length - 1) {
            state.historyIndex++;
            state.workflowDefinition = JSON.parse(JSON.stringify(state.history[state.historyIndex]));
            state.selectedNode = undefined;
            state.selectedEdge = undefined;
            state.isDirty = true;
          }
        }),
        
        clearHistory: () => set((state) => {
          state.history = [];
          state.historyIndex = -1;
        }),
        
        // Validation
        validateWorkflow: () => {
          const errors = validateWorkflowDefinition(get().workflowDefinition);
          set((state) => {
            state.validationErrors = errors;
          });
          return errors;
        },
        
        clearValidationErrors: () => set((state) => {
          state.validationErrors = [];
        }),
        
        // Collaboration
        addCollaborator: (collaborator) => set((state) => {
          if (!state.collaborators.find(c => c.id === collaborator.id)) {
            state.collaborators.push(collaborator);
          }
        }),
        
        removeCollaborator: (collaboratorId) => set((state) => {
          state.collaborators = state.collaborators.filter(c => c.id !== collaboratorId);
        }),
        
        updateCollaboratorCursor: (collaboratorId, cursor) => set((state) => {
          const collaborator = state.collaborators.find(c => c.id === collaboratorId);
          if (collaborator) {
            collaborator.cursor = cursor;
          }
        }),
        
        // UI state
        setSidebarOpen: (open) => set((state) => {
          state.sidebarOpen = open;
        }),
        
        setActiveTab: (tab) => set((state) => {
          state.activeTab = tab;
        }),
        
        setZoomLevel: (level) => set((state) => {
          state.zoomLevel = Math.max(0.1, Math.min(3, level));
        }),
        
        setLoading: (loading) => set((state) => {
          state.isLoading = loading;
        }),
        
        setSaving: (saving) => set((state) => {
          state.isSaving = saving;
        }),
        
        // Workflow operations
        saveWorkflow: async () => {
          set((state) => {
            state.isSaving = true;
          });
          
          try {
            // TODO: Implement API call to save workflow
            // await workflowAPI.saveWorkflow(get().workflowDefinition);
            
            set((state) => {
              state.isDirty = false;
              state.workflowDefinition.updatedAt = new Date();
            });
          } catch (error) {
            console.error('Failed to save workflow:', error);
            throw error;
          } finally {
            set((state) => {
              state.isSaving = false;
            });
          }
        },
        
        loadWorkflow: async (workflowId) => {
          set((state) => {
            state.isLoading = true;
          });
          
          try {
            // TODO: Implement API call to load workflow
            // const workflow = await workflowAPI.getWorkflow(workflowId);
            // get().setWorkflowDefinition(workflow);
          } catch (error) {
            console.error('Failed to load workflow:', error);
            throw error;
          } finally {
            set((state) => {
              state.isLoading = false;
            });
          }
        },
        
        exportWorkflow: () => {
          const workflow = get().workflowDefinition;
          return JSON.stringify(workflow, null, 2);
        },
        
        importWorkflow: (data) => {
          try {
            const workflow = JSON.parse(data);
            set((state) => {
              state.workflowDefinition = workflow;
              state.isDirty = true;
              state.addToHistory();
            });
          } catch (error) {
            console.error('Failed to import workflow:', error);
            throw new Error('Invalid workflow data');
          }
        },
        
        // Reset store
        resetStore: () => set((state) => {
          Object.assign(state, {
            workflowDefinition: createEmptyWorkflow(),
            selectedNode: undefined,
            selectedEdge: undefined,
            isEditing: false,
            isDirty: false,
            history: [],
            historyIndex: -1,
            clipboard: undefined,
            validationErrors: [],
            collaborators: [],
            isLoading: false,
            isSaving: false,
            isValidating: false,
            sidebarOpen: true,
            activeTab: 'properties',
            zoomLevel: 1
          });
        })
      }))
    ),
    { name: 'workflow-store' }
  )
); 