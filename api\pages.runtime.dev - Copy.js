(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,a={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function i(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,o],...a]=i(e),{domain:s,expires:l,httponly:c,maxage:p,path:m,samesite:f,secure:h,priority:g}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t])),v={name:n,value:decodeURIComponent(o),domain:s,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:m,...f&&{sameSite:d.includes(t=(t=f).toLowerCase())?t:void 0},...h&&{secure:!0},...g&&{priority:u.includes(r=(r=g).toLowerCase())?r:void 0}};return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}(v)}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>c,ResponseCookies:()=>p,parseCookie:()=>i,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,a,s,i)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let s of n(a))o.call(e,s)||void 0===s||t(e,s,{get:()=>a[s],enumerable:!(i=r(a,s))||i.enumerable});return e})(t({},"__esModule",{value:!0}),a);var d=["strict","lax","none"],u=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t){let e=i(t);for(let[t,r]of e)this._parsed.set(t,{name:t,value:r})}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[],a=Array.isArray(o)?o:function(e){if(!e)return[];var t,r,n,o,a,s=[],i=0;function l(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,a=!1;l();)if(","===(r=e.charAt(i))){for(n=i,i+=1,l(),o=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(a=!0,i=o,s.push(e.substring(t,n)),t=i):i=n+1}else i+=1;(!a||i>=e.length)&&s.push(e.substring(t,e.length))}return s}(o);for(let e of a){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},a=t.split(n),s=(r||{}).decode||e,i=0;i<a.length;i++){var l=a[i],d=l.indexOf("=");if(!(d<0)){var u=l.substr(0,d).trim(),c=l.substr(++d,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==o[u]&&(o[u]=function(e,t){try{return t(e)}catch(t){return e}}(c,s))}}return o},t.serialize=function(e,t,n){var a=n||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var i=s(t);if(i&&!o.test(i))throw TypeError("argument val is invalid");var l=e+"="+i;if(null!=a.maxAge){var d=a.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(d)}if(a.domain){if(!o.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!o.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},"./dist/compiled/fresh/index.js":e=>{(()=>{"use strict";var t={695:e=>{/*!
 * fresh
 * Copyright(c) 2012 TJ Holowaychuk
 * Copyright(c) 2016-2017 Douglas Christopher Wilson
 * MIT Licensed
 */var t=/(?:^|,)\s*?no-cache\s*?(?:,|$)/;function r(e){var t=e&&Date.parse(e);return"number"==typeof t?t:NaN}e.exports=function(e,n){var o=e["if-modified-since"],a=e["if-none-match"];if(!o&&!a)return!1;var s=e["cache-control"];if(s&&t.test(s))return!1;if(a&&"*"!==a){var i=n.etag;if(!i)return!1;for(var l=!0,d=function(e){for(var t=0,r=[],n=0,o=0,a=e.length;o<a;o++)switch(e.charCodeAt(o)){case 32:n===t&&(n=t=o+1);break;case 44:r.push(e.substring(n,t)),n=t=o+1;break;default:t=o+1}return r.push(e.substring(n,t)),r}(a),u=0;u<d.length;u++){var c=d[u];if(c===i||c==="W/"+i||"W/"+c===i){l=!1;break}}if(l)return!1}if(o){var p=n["last-modified"];if(!p||!(r(p)<=r(o)))return!1}return!0}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}},s=!0;try{t[e](a,a.exports,n),s=!1}finally{s&&delete r[e]}return a.exports}n.ab=__dirname+"/";var o=n(695);e.exports=o})()},"./dist/compiled/react-is/cjs/react-is.development.js":(e,t)=>{"use strict";!function(){var e,r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),l=Symbol.for("react.context"),d=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.for("react.offscreen");function g(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:var h=e.type;switch(h){case o:case s:case a:case c:case p:return h;default:var g=h&&h.$$typeof;switch(g){case d:case l:case u:case f:case m:case i:return g;default:return t}}case n:return t}}}e=Symbol.for("react.module.reference");var v=!1,y=!1;t.ContextConsumer=l,t.ContextProvider=i,t.Element=r,t.ForwardRef=u,t.Fragment=o,t.Lazy=f,t.Memo=m,t.Portal=n,t.Profiler=s,t.StrictMode=a,t.Suspense=c,t.SuspenseList=p,t.isAsyncMode=function(e){return v||(v=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")),!1},t.isConcurrentMode=function(e){return y||(y=!0,console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")),!1},t.isContextConsumer=function(e){return g(e)===l},t.isContextProvider=function(e){return g(e)===i},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return g(e)===u},t.isFragment=function(e){return g(e)===o},t.isLazy=function(e){return g(e)===f},t.isMemo=function(e){return g(e)===m},t.isPortal=function(e){return g(e)===n},t.isProfiler=function(e){return g(e)===s},t.isStrictMode=function(e){return g(e)===a},t.isSuspense=function(e){return g(e)===c},t.isSuspenseList=function(e){return g(e)===p},t.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===o||t===s||t===a||t===c||t===p||t===h||"object"==typeof t&&null!==t&&(t.$$typeof===f||t.$$typeof===m||t.$$typeof===i||t.$$typeof===l||t.$$typeof===u||t.$$typeof===e||void 0!==t.getModuleId)},t.typeOf=g}()},"./dist/compiled/react-is/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react-is/cjs/react-is.development.js")},"./dist/compiled/strip-ansi/index.js":e=>{(()=>{"use strict";var t={511:e=>{e.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")},532:(e,t,r)=>{let n=r(511);e.exports=e=>"string"==typeof e?e.replace(n(),""):e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}},s=!0;try{t[e](a,a.exports,n),s=!1}finally{s&&delete r[e]}return a.exports}n.ab=__dirname+"/";var o=n(532);e.exports=o})()},"./dist/esm/build/output/log.js":(e,t,r)=>{"use strict";var n;r.d(t,{ZK:()=>v});let{env:o,stdout:a}=(null==(n=globalThis)?void 0:n.process)??{},s=o&&!o.NO_COLOR&&(o.FORCE_COLOR||(null==a?void 0:a.isTTY)&&!o.CI&&"dumb"!==o.TERM),i=(e,t,r,n)=>{let o=e.substring(0,n)+r,a=e.substring(n+t.length),s=a.indexOf(t);return~s?o+i(a,t,r,s):o+a},l=(e,t,r=e)=>n=>{let o=""+n,a=o.indexOf(t,e.length);return~a?e+i(o,t,r,a)+t:e+o+t},d=s?l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m"):String;s&&l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),s&&l("\x1b[3m","\x1b[23m"),s&&l("\x1b[4m","\x1b[24m"),s&&l("\x1b[7m","\x1b[27m"),s&&l("\x1b[8m","\x1b[28m"),s&&l("\x1b[9m","\x1b[29m"),s&&l("\x1b[30m","\x1b[39m");let u=s?l("\x1b[31m","\x1b[39m"):String,c=s?l("\x1b[32m","\x1b[39m"):String,p=s?l("\x1b[33m","\x1b[39m"):String;s&&l("\x1b[34m","\x1b[39m");let m=s?l("\x1b[35m","\x1b[39m"):String;s&&l("\x1b[38;2;173;127;168m","\x1b[39m"),s&&l("\x1b[36m","\x1b[39m");let f=s?l("\x1b[37m","\x1b[39m"):String;s&&l("\x1b[90m","\x1b[39m"),s&&l("\x1b[40m","\x1b[49m"),s&&l("\x1b[41m","\x1b[49m"),s&&l("\x1b[42m","\x1b[49m"),s&&l("\x1b[43m","\x1b[49m"),s&&l("\x1b[44m","\x1b[49m"),s&&l("\x1b[45m","\x1b[49m"),s&&l("\x1b[46m","\x1b[49m"),s&&l("\x1b[47m","\x1b[49m");let h={wait:f(d("○")),error:u(d("⨯")),warn:p(d("⚠")),ready:d("▲"),info:f(d(" ")),event:c(d("✓")),trace:m(d("\xbb"))},g={log:"log",warn:"warn",error:"error"};function v(...e){!function(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in g?g[e]:"log",n=h[e];0===t.length?console[r](""):console[r](" "+n,...t)}("warn",...e)}},"./dist/esm/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{BR:()=>a,Ei:()=>d,Eo:()=>p,Lx:()=>c,Qq:()=>o,Wo:()=>i,lk:()=>m,oL:()=>l,q6:()=>u,wh:()=>s,y3:()=>n});let n="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",a=31536e3,s="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",i="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",l="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",d="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",u="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",c="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",p="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",m="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",f={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...f,GROUP:{server:[f.reactServerComponents,f.actionBrowser,f.appMetadataRoute,f.appRouteHandler],nonClientServerTarget:[f.middleware,f.api],app:[f.reactServerComponents,f.actionBrowser,f.appMetadataRoute,f.appRouteHandler,f.serverSideRendering,f.appPagesBrowser]}})},"./dist/esm/lib/non-nullable.js":(e,t,r)=>{"use strict";function n(e){return null!=e}r.d(t,{v:()=>n})},"./dist/esm/server/api-utils/index.js":(e,t,r)=>{"use strict";r.d(t,{Di:()=>l,Iq:()=>a,Lm:()=>u,QM:()=>i,dS:()=>s,gk:()=>c});var n=r("./dist/esm/server/web/spec-extension/adapters/headers.js"),o=r("./dist/esm/lib/constants.js");function a(e,t){let r=n.h.from(e.headers),a=r.get(o.y3),s=a===t.previewModeId,i=r.has(o.Qq);return{isOnDemandRevalidate:s,revalidateOnlyGenerated:i}}let s="__prerender_bypass",i="__next_preview_data",l=Symbol(i),d=Symbol(s);function u(e,t={}){if(d in e)return e;let{serialize:n}=r("./dist/compiled/cookie/index.js"),o=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof o?[o]:Array.isArray(o)?o:[],n(s,"",{expires:new Date(0),httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==t.path?{path:t.path}:void 0}),n(i,"",{expires:new Date(0),httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==t.path?{path:t.path}:void 0})]),Object.defineProperty(e,d,{value:!0,enumerable:!1}),e}function c({req:e},t,r){let n={configurable:!0,enumerable:!0},o={...n,writable:!0};Object.defineProperty(e,t,{...n,get:()=>{let n=r();return Object.defineProperty(e,t,{...o,value:n}),n},set:r=>{Object.defineProperty(e,t,{...o,value:r})}})}},"./dist/esm/server/api-utils/node/try-get-preview-data.js":(e,t,r)=>{"use strict";r.d(t,{R:()=>s});var n=r("./dist/esm/server/api-utils/index.js"),o=r("./dist/esm/server/web/spec-extension/cookies.js"),a=r("./dist/esm/server/web/spec-extension/adapters/headers.js");function s(e,t,s){var i,l;let d;if(s&&(0,n.Iq)(e,s).isOnDemandRevalidate)return!1;if(n.Di in e)return e[n.Di];let u=a.h.from(e.headers),c=new o.q(u),p=null==(i=c.get(n.dS))?void 0:i.value,m=null==(l=c.get(n.QM))?void 0:l.value;if(p&&!m&&p===s.previewModeId){let t={};return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}if(!p&&!m)return!1;if(!p||!m||p!==s.previewModeId)return(0,n.Lm)(t),!1;try{let e=r("next/dist/compiled/jsonwebtoken");d=e.verify(m,s.previewModeSigningKey)}catch{return(0,n.Lm)(t),!1}let{decryptWithSecret:f}=r("./dist/esm/server/crypto-utils.js"),h=f(Buffer.from(s.previewModeEncryptionKey),d.data);try{let t=JSON.parse(h);return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}catch{return!1}}},"./dist/esm/server/crypto-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{decryptWithSecret:()=>i,encryptWithSecret:()=>s});let n=require("crypto");var o=r.n(n);let a="aes-256-gcm";function s(e,t){let r=o().randomBytes(16),n=o().randomBytes(64),s=o().pbkdf2Sync(e,n,1e5,32,"sha512"),i=o().createCipheriv(a,s,r),l=Buffer.concat([i.update(t,"utf8"),i.final()]),d=i.getAuthTag();return Buffer.concat([n,r,d,l]).toString("hex")}function i(e,t){let r=Buffer.from(t,"hex"),n=r.slice(0,64),s=r.slice(64,80),i=r.slice(80,96),l=r.slice(96),d=o().pbkdf2Sync(e,n,1e5,32,"sha512"),u=o().createDecipheriv(a,d,s);return u.setAuthTag(i),u.update(l)+u.final("utf8")}},"./dist/esm/server/font-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{calculateOverrideValues:()=>m,calculateSizeAdjustValues:()=>f,getFontDefinitionFromManifest:()=>u,getFontDefinitionFromNetwork:()=>d,getFontOverrideCss:()=>v});var n=r("./dist/esm/build/output/log.js"),o=r("./dist/esm/shared/lib/constants.js");let a=r("next/dist/server/capsize-font-metrics.json"),s=r("https");function i(e){return e.startsWith(o.Lw)}function l(e,t){return new Promise((r,n)=>{let o="";s.get(e,{headers:{"user-agent":t}},e=>{e.on("data",e=>{o+=e}),e.on("end",()=>{r(o.toString("utf8"))})}).on("error",e=>{n(e)})})}async function d(e){let t="";try{i(e)&&(t+=await l(e,"Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko")),t+=await l(e,"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36")}catch(t){return n.ZK(`Failed to download the stylesheet for ${e}. Skipped optimizing this font.`),""}return t}function u(e,t){var r;return(null==(r=t.find(t=>!!t&&t.url===e))?void 0:r.content)||""}function c(e){return e.replace(/(?:^\w|[A-Z]|\b\w)/g,function(e,t){return 0===t?e.toLowerCase():e.toUpperCase()}).replace(/\s+/g,"")}function p(e){return Math.abs(100*e).toFixed(2)}function m(e){let t=c(e),r=a[t],{category:n,ascent:s,descent:i,lineGap:l,unitsPerEm:d}=r,u="serif"===n?o.oh:o.dW;return{ascent:s=p(s/d),descent:i=p(i/d),lineGap:l=p(l/d),fallbackFont:u.name}}function f(e){let t=c(e),r=a[t],{category:n,ascent:s,descent:i,lineGap:l,unitsPerEm:d,xWidthAvg:u}=r,m=u/d,f="serif"===n?o.oh:o.dW,h=c(f.name),g=a[h],v=g.xWidthAvg/g.unitsPerEm,y=u?m/v:1;return{ascent:s=p(s/(d*y)),descent:i=p(i/(d*y)),lineGap:l=p(l/(d*y)),fallbackFont:f.name,sizeAdjust:p(y)}}function h(e){let t=e.trim(),{ascent:r,descent:n,lineGap:o,fallbackFont:a}=m(t);return`
    @font-face {
      font-family: "${t} Fallback";
      ascent-override: ${r}%;
      descent-override: ${n}%;
      line-gap-override: ${o}%;
      src: local("${a}");
    }
  `}function g(e){let t=e.trim(),{ascent:r,descent:n,lineGap:o,fallbackFont:a,sizeAdjust:s}=f(t);return`
    @font-face {
      font-family: "${t} Fallback";
      ascent-override: ${r}%;
      descent-override: ${n}%;
      line-gap-override: ${o}%;
      size-adjust: ${s}%;
      src: local("${a}");
    }
  `}function v(e,t,r=!1){if(!i(e))return"";let n=r?g:h;try{let e=function(e){let t=e.matchAll(/font-family: ([^;]*)/g),r=new Set;for(let e of t){let t=e[1].replace(/^['"]|['"]$/g,"");r.add(t)}return[...r]}(t),r=e.reduce((e,t)=>e+=n(t),"");return r}catch(e){return console.log("Error getting font override values - ",e),""}}},"./dist/esm/server/node-polyfill-web-streams.js":(e,t,r)=>{if(!global.ReadableStream){if(r("stream/web").ReadableStream)global.ReadableStream=r("stream/web").ReadableStream;else{let{ReadableStream:e}=r("next/dist/compiled/@edge-runtime/ponyfill");global.ReadableStream=e}}if(!global.TransformStream){if(r("stream/web").TransformStream)global.TransformStream=r("stream/web").TransformStream;else{let{TransformStream:e}=r("next/dist/compiled/@edge-runtime/ponyfill");global.TransformStream=e}}},"./dist/esm/server/optimize-amp.js":(e,t,r)=>{"use strict";async function n(e,t){let n;try{n=r("next/dist/compiled/@ampproject/toolbox-optimizer")}catch(t){return e}let o=n.create(t);return o.transformHtml(e,t)}r.d(t,{Z:()=>n})},"./dist/esm/server/post-process.js":(e,t,r)=>{"use strict";r.d(t,{X:()=>l});var n,o=r("./dist/esm/shared/lib/constants.js"),a=r("./dist/esm/lib/non-nullable.js");let s=[];async function i(e,t,n){if(!s[0])return e;let{parse:o}=r("next/dist/compiled/node-html-parser"),a=o(e),i=e;async function l(e){let r=e.inspect(a,t);i=await e.mutate(i,r,t)}for(let e=0;e<s.length;e++){let t=s[e];(!t.condition||t.condition(n))&&await l(s[e].middleware)}return i}async function l(e,t,n,{inAmpMode:o,hybridAmp:s}){let l=[o?async t=>{let o=r("./dist/esm/server/optimize-amp.js").Z;return t=await o(t,n.ampOptimizerConfig),!n.ampSkipValidation&&n.ampValidator&&await n.ampValidator(t,e),t}:null,(0,n.optimizeFonts)?async e=>await i(e,{getFontDefinition:e=>{if(n.fontManifest){let{getFontDefinitionFromManifest:t}=r("./dist/esm/server/font-utils.js");return t(e,n.fontManifest)}return""}},{optimizeFonts:n.optimizeFonts}):null,(0,n.optimizeCss)?async e=>{let t=r("critters"),o=new t({ssrMode:!0,reduceInlineStyles:!1,path:n.distDir,publicPath:`${n.assetPrefix}/_next/`,preload:"media",fonts:!1,...n.optimizeCss});return await o.process(e)}:null,o||s?e=>e.replace(/&amp;amp=1/g,"&amp=1"):null].filter(a.v);for(let e of l)e&&(t=await e(t));return t}n=new class{inspect(e,t){if(!t.getFontDefinition)return;let r=[];return e.querySelectorAll("link").filter(e=>"stylesheet"===e.getAttribute("rel")&&e.hasAttribute("data-href")&&o.C7.some(({url:t})=>{let r=e.getAttribute("data-href");return!!r&&r.startsWith(t)})).forEach(e=>{let t=e.getAttribute("data-href"),n=e.getAttribute("nonce");t&&r.push([t,n])}),r}constructor(){this.mutate=async(e,t,r)=>{let n=e,a=new Set;if(!r.getFontDefinition)return e;t.forEach(e=>{let[t,s]=e,i=`<link rel="stylesheet" href="${t}"/>`;if(n.indexOf(`<style data-href="${t}">`)>-1||n.indexOf(i)>-1)return;let l=r.getFontDefinition?r.getFontDefinition(t):null;if(l){let e=s?` nonce="${s}"`:"",r="";l.includes("ascent-override")&&(r=' data-size-adjust="true"'),n=n.replace("</head>",`<style data-href="${t}"${e}${r}>${l}</style></head>`);let i=t.replace(/&/g,"&amp;").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),d=RegExp(`<link[^>]*data-href="${i}"[^>]*/>`);n=n.replace(d,"");let u=o.C7.find(e=>t.startsWith(e.url));u&&a.add(u.preconnect)}else n=n.replace("</head>",`${i}</head>`)});let s="";return a.forEach(e=>{s+=`<link rel="preconnect" href="${e}" crossorigin />`}),n=n.replace('<meta name="next-font-preconnect"/>',s)}}},s.push({name:"Inline-Fonts",middleware:n,condition:(e=>e.optimizeFonts||process.env.__NEXT_OPTIMIZE_FONTS)||null})},"./dist/esm/server/web/spec-extension/adapters/headers.js":(e,t,r)=>{"use strict";r.d(t,{h:()=>a});var n=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.g.get(t,r,o);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==s)return n.g.get(t,s,o)},set(t,r,o,a){if("symbol"==typeof r)return n.g.set(t,r,o,a);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return n.g.set(t,i??r,o,a)},has(t,r){if("symbol"==typeof r)return n.g.has(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==a&&n.g.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return n.g.deleteProperty(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===a||n.g.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},"./dist/esm/server/web/spec-extension/cookies.js":(e,t,r)=>{"use strict";r.d(t,{q:()=>n.RequestCookies});var n=r("./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{C7:()=>u,Er:()=>m,Lw:()=>d,NO:()=>i,dW:()=>p,fn:()=>s,o3:()=>a,oh:()=>c,uY:()=>l,wU:()=>o}),r("./dist/esm/shared/lib/modern-browserslist-target.js");let n={client:"client",server:"server",edgeServer:"edge-server"};n.client,n.server,n.edgeServer;let o="__NEXT_BUILTIN_DOCUMENT__";Symbol("polyfills");let a=307,s=308,i="__N_SSG",l="__N_SSP",d="https://fonts.googleapis.com/",u=[{url:d,preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],c={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},p={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},m=["/500"]},"./dist/esm/shared/lib/modern-browserslist-target.js":e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},critters:e=>{"use strict";e.exports=require("critters")},"next/dist/compiled/@ampproject/toolbox-optimizer":e=>{"use strict";e.exports=require("next/dist/compiled/@ampproject/toolbox-optimizer")},"next/dist/compiled/@edge-runtime/ponyfill":e=>{"use strict";e.exports=require("next/dist/compiled/@edge-runtime/ponyfill")},"next/dist/compiled/@next/react-dev-overlay/dist/middleware":e=>{"use strict";e.exports=require("next/dist/compiled/@next/react-dev-overlay/dist/middleware")},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/node-html-parser":e=>{"use strict";e.exports=require("next/dist/compiled/node-html-parser")},"next/dist/server/capsize-font-metrics.json":e=>{"use strict";e.exports=require("next/dist/server/capsize-font-metrics.json")},https:e=>{"use strict";e.exports=require("https")},path:e=>{"use strict";e.exports=require("path")},"stream/web":e=>{"use strict";e.exports=require("stream/web")}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";let e,t,o;r.r(n),r.d(n,{PagesRouteModule:()=>eQ,default:()=>eX,renderToHTML:()=>eY,vendored:()=>eK});var a,s,i,l,d,u,c,p,m,f,h,g,v={};r.r(v),r.d(v,{AmpStateContext:()=>z});var y={};r.r(y),r.d(y,{HeadManagerContext:()=>B});var x={};r.r(x),r.d(x,{LoadableContext:()=>W});var b={};r.r(b),r.d(b,{default:()=>Q});var S={};r.r(S),r.d(S,{RouterContext:()=>K});var w={};r.r(w),r.d(w,{HtmlContext:()=>es,useHtmlContext:()=>ei});var _={};r.r(_),r.d(_,{ImageConfigContext:()=>ew});var P={};r.r(P),r.d(P,{PathParamsContext:()=>eT,PathnameContext:()=>eR,SearchParamsContext:()=>eE});var C={};r.r(C),r.d(C,{AppRouterContext:()=>eM,CacheStates:()=>g,GlobalLayoutRouterContext:()=>eL,LayoutRouterContext:()=>eI,TemplateContext:()=>eD});var E={};r.r(E),r.d(E,{ServerInsertedHTMLContext:()=>eJ,useServerInsertedHTML:()=>eV});var R={};r.r(R),r.d(R,{AmpContext:()=>v,AppRouterContext:()=>C,HeadManagerContext:()=>y,HooksClientContext:()=>P,HtmlContext:()=>w,ImageConfigContext:()=>_,Loadable:()=>b,LoadableContext:()=>x,RouterContext:()=>S,ServerInsertedHtml:()=>E});class T{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}var j=r("./dist/esm/server/api-utils/index.js");let $=require("react");var A=r.n($);let N=require("react-dom/server.browser");var O=r.n(N);let k=require("styled-jsx");var M=r("./dist/esm/lib/constants.js"),I=r("./dist/esm/shared/lib/constants.js");function L(e){return Object.prototype.toString.call(e)}function D(e){if("[object Object]"!==L(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}let F=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class q extends Error{constructor(e,t,r,n){super(r?`Error serializing \`${r}\` returned from \`${t}\` in "${e}".
Reason: ${n}`:`Error serializing props returned from \`${t}\` in "${e}".
Reason: ${n}`)}}function H(e,t,r){if(!D(r))throw new q(e,t,"",`Props must be returned as a plain object from ${t}: \`{ props: { ... } }\` (received: \`${L(r)}\`).`);function n(r,n,o){if(r.has(n))throw new q(e,t,o,`Circular references cannot be expressed in JSON (references: \`${r.get(n)||"(self)"}\`).`);r.set(n,o)}return function r(o,a,s){let i=typeof a;if(null===a||"boolean"===i||"number"===i||"string"===i)return!0;if("undefined"===i)throw new q(e,t,s,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value.");if(D(a)){if(n(o,a,s),Object.entries(a).every(([e,t])=>{let n=F.test(e)?`${s}.${e}`:`${s}[${JSON.stringify(e)}]`,a=new Map(o);return r(a,e,n)&&r(a,t,n)}))return!0;throw new q(e,t,s,"invariant: Unknown error encountered in Object.")}if(Array.isArray(a)){if(n(o,a,s),a.every((e,t)=>{let n=new Map(o);return r(n,e,`${s}[${t}]`)}))return!0;throw new q(e,t,s,"invariant: Unknown error encountered in Array.")}throw new q(e,t,s,"`"+i+"`"+("object"===i?` ("${Object.prototype.toString.call(a)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types.")}(new Map,r,"")}let z=A().createContext({});z.displayName="AmpStateContext";let B=A().createContext({});B.displayName="HeadManagerContext";let W=A().createContext(null);W.displayName="LoadableContext";let U=[],G=[];function Z(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class Y{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function J(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function o(){if(!n){let t=new Y(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}function a(e,t){!function(){o();let e=A().useContext(W);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let a=A().useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return A().useImperativeHandle(t,()=>({retry:n.retry}),[]),A().useMemo(()=>{var t;return a.loading||a.error?A().createElement(r.loading,{isLoading:a.loading,pastDelay:a.pastDelay,timedOut:a.timedOut,error:a.error,retry:n.retry}):a.loaded?A().createElement((t=a.loaded)&&t.default?t.default:t,e):null},[e,a])}return U.push(o),a.preload=()=>o(),a.displayName="LoadableComponent",A().forwardRef(a)}(Z,e)}function V(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return V(e,t)})}J.preloadAll=()=>new Promise((e,t)=>{V(U).then(e,t)}),J.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>t();V(G,e).then(r,r)}));let Q=J,K=A().createContext(null);K.displayName="RouterContext";let X=/\/\[[^/]+?\](?=\/|$)/;function ee(e){return X.test(e)}function et(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function er(e){return e.finished||e.headersSent}async function en(e,t){var r;if(null==(r=e.prototype)?void 0:r.getInitialProps){let t='"'+et(e)+'.getInitialProps()" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';throw Error(t)}let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await en(t.Component,t.ctx)}:{};let o=await e.getInitialProps(t);if(n&&er(n))return o;if(!o){let t='"'+et(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.';throw Error(t)}return 0!==Object.keys(o).length||t.ctx||console.warn(""+et(e)+" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps"),o}let eo="undefined"!=typeof performance;eo&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class ea extends Error{}let es=(0,$.createContext)(void 0);function ei(){let e=(0,$.useContext)(es);if(!e)throw Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page");return e}es.displayName="HtmlContext";let el=Symbol.for("NextInternalRequestMeta");function ed(e,t){let r=e[el]||{};return"string"==typeof t?r[t]:r}let eu=new Set([301,302,303,307,308]);function ec(e){return e.statusCode||(e.permanent?I.fn:I.o3)}async function ep(e,t,r){let n=e.getReader(),o=!1,a=!1;function s(){a=!0,t.off("close",s),o||(o=!0,n.cancel().catch(()=>{}))}t.on("close",s);try{for(;;){let{done:e,value:r}=await n.read();if(o=e,e||a)break;r&&(t.write(r),null==t.flush||t.flush.call(t))}}catch(e){if((null==e?void 0:e.name)!=="AbortError")throw e}finally{t.off("close",s),o||n.cancel().catch(()=>{}),r&&await r,a||t.end()}}class em{static fromStatic(e){return new em(e)}constructor(e,{contentType:t,waitUntil:r,...n}={}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}extendMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(){if("string"!=typeof this.response)throw Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return this.response}async pipe(e){if(null===this.response)throw Error("Invariant: response is null. This is a bug in Next.js");if("string"==typeof this.response)throw Error("Invariant: static responses cannot be piped. This is a bug in Next.js");return await ep(this.response,e,this.waitUntil)}}var ef=r("./dist/esm/lib/non-nullable.js");let eh=require("next/dist/server/lib/trace/tracer");function eg(e){return new TextEncoder().encode(e)}function ev(e,t){return t.decode(e,{stream:!0})}(function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"})(a||(a={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(s||(s={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(i||(i={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(l||(l={})),(d||(d={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(u||(u={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(c||(c={})),(p||(p={})).executeRoute="Router.executeRoute",(m||(m={})).runHandler="Node.runHandler",(f||(f={})).runHandler="AppRouteRouteHandlers.runHandler",(h||(h={})).generateMetadata="ResolveMetadata.generateMetadata";let ey=setImmediate;function ex(e){return new ReadableStream({start(t){t.enqueue(eg(e)),t.close()}})}async function eb(e){let t=e.getReader(),r=new TextDecoder,n="";for(;;){let{done:e,value:o}=await t.read();if(e)return n;n+=ev(o,r)}}async function eS(e,{suffix:t,inlinedDataStream:r,generateStaticHTML:n,getServerInsertedHTML:o,serverInsertedHTMLToHead:a,validateRootLayout:s}){let i,l,d;let u="</body></html>",c=t?t.split(u)[0]:null;n&&await e.allReady;let p=[function(){let e=new Uint8Array,t=null,r=r=>{t||(t=new Promise(n=>{ey(()=>{r.enqueue(e),e=new Uint8Array,t=null,n()})}))};return new TransformStream({transform(t,n){let o=new Uint8Array(e.length+t.byteLength);o.set(e),o.set(t,e.length),e=o,r(n)},flush(){if(t)return t}})}(),o&&!a?new TransformStream({async transform(e,t){let r=eg(await o());t.enqueue(r),t.enqueue(e)}}):null,null!=c?(i=!1,l=null,new TransformStream({transform(e,t){t.enqueue(e),!i&&c.length&&(i=!0,l=new Promise(e=>{ey(()=>{t.enqueue(eg(c)),e()})}))},flush(e){if(l)return l;!i&&c.length&&(i=!0,e.enqueue(eg(c)))}})):null,r?(d=null,new TransformStream({transform(e,t){if(t.enqueue(e),!d){let e=r.getReader();d=new Promise(r=>setTimeout(async()=>{try{for(;;){let{done:n,value:o}=await e.read();if(n)return r();t.enqueue(o)}}catch(e){t.error(e)}r()},0))}},flush(){if(d)return d}})):null,function(e){let t=!1,r=new TextDecoder;return new TransformStream({transform(n,o){if(!e||t)return o.enqueue(n);let a=ev(n,r);if(a.endsWith(e)){t=!0;let r=a.slice(0,-e.length);o.enqueue(eg(r))}else o.enqueue(n)},flush(t){e&&t.enqueue(eg(e))}})}(u),o&&a?function(e){let t=!1,r=!1,n=new TextDecoder;return new TransformStream({async transform(o,a){if(r){a.enqueue(o);return}let s=await e();if(t)a.enqueue(eg(s)),a.enqueue(o),r=!0;else{let e=ev(o,n),i=e.indexOf("</head>");if(-1!==i){let n=e.slice(0,i)+s+e.slice(i);a.enqueue(eg(n)),r=!0,t=!0}}t?ey(()=>{r=!1}):a.enqueue(o)},async flush(t){let r=await e();r&&t.enqueue(eg(r))}})}(o):null,s?function(e="",t){let r=!1,n=!1,o=new TextDecoder;return new TransformStream({async transform(e,t){if(!r||!n){let t=ev(e,o);!r&&t.includes("<html")&&(r=!0),!n&&t.includes("<body")&&(n=!0)}t.enqueue(e)},flush(o){if(!r||!n){let a=[r?null:"html",n?null:"body"].filter(ef.v);o.enqueue(eg(`<script>self.__next_root_layout_missing_tags_error=${JSON.stringify({missingTags:a,assetPrefix:e??"",tree:t()})}</script>`))}}})}(s.assetPrefix,s.getTree):null].filter(ef.v);return p.reduce((e,t)=>e.pipeThrough(t),e)}let ew=A().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1});ew.displayName="ImageConfigContext";var e_=r("./dist/compiled/strip-ansi/index.js"),eP=r.n(e_);let eC=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],eE=(0,$.createContext)(null),eR=(0,$.createContext)(null),eT=(0,$.createContext)(null);eE.displayName="SearchParamsContext",eR.displayName="PathnameContext",eT.displayName="PathParamsContext";let ej=["(..)(..)","(.)","(..)","(...)"],e$=/[|\\{}()[\]^$+*?.-]/,eA=/[|\\{}()[\]^$+*?.-]/g;function eN(e){return e$.test(e)?e.replace(eA,"\\$&"):e}function eO(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function ek(e){let{children:t,router:r,...n}=e,o=(0,$.useRef)(n.isAutoExport),a=(0,$.useMemo)(()=>{let e;let t=o.current;if(t&&(o.current=!1),ee(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return A().createElement(eR.Provider,{value:a},t)}!function(e){e.LAZY_INITIALIZED="LAZYINITIALIZED",e.DATA_FETCH="DATAFETCH",e.READY="READY"}(g||(g={}));let eM=A().createContext(null),eI=A().createContext(null),eL=A().createContext(null),eD=A().createContext(null);eM.displayName="AppRouterContext",eI.displayName="LayoutRouterContext",eL.displayName="GlobalLayoutRouterContext",eD.displayName="TemplateContext";var eF=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");r("./dist/compiled/fresh/index.js");let eq="<!DOCTYPE html>";function eH(){throw Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance')}async function ez(e){let t=await O().renderToReadableStream(e);return await t.allReady,eb(t)}r("./dist/esm/server/node-polyfill-web-streams.js"),e=r("./dist/esm/server/api-utils/node/try-get-preview-data.js").R,t=r("./dist/esm/build/output/log.js").ZK,o=r("./dist/esm/server/post-process.js").X;class eB{constructor(e,t,r,{isFallback:n},o,a,s,i,l,d,u,c){this.route=e.replace(/\/$/,"")||"/",this.pathname=e,this.query=t,this.asPath=r,this.isFallback=n,this.basePath=a,this.locale=s,this.locales=i,this.defaultLocale=l,this.isReady=o,this.domainLocales=d,this.isPreview=!!u,this.isLocaleDomain=!!c}push(){eH()}replace(){eH()}reload(){eH()}back(){eH()}forward(){eH()}prefetch(){eH()}beforePopState(){eH()}}function eW(e,t,r){return A().createElement(e,{Component:t,...r})}let eU=(e,t)=>{let r=`invalid-${e.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${e}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:

	return { props: { title: 'My Title', content: '...' } }

Keys that need to be moved: ${t.join(", ")}.
Read more: https://nextjs.org/docs/messages/${r}`};function eG(e,t,r){let{destination:n,permanent:o,statusCode:a,basePath:s}=e,i=[],l=void 0!==a,d=void 0!==o;d&&l?i.push("`permanent` and `statusCode` can not both be provided"):d&&"boolean"!=typeof o?i.push("`permanent` must be `true` or `false`"):l&&!eu.has(a)&&i.push(`\`statusCode\` must undefined or one of ${[...eu].join(", ")}`);let u=typeof n;"string"!==u&&i.push(`\`destination\` should be string but received ${u}`);let c=typeof s;if("undefined"!==c&&"boolean"!==c&&i.push(`\`basePath\` should be undefined or a false, received ${c}`),i.length>0)throw Error(`Invalid redirect object returned from ${r} for ${t.url}
`+i.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp")}async function eZ(n,a,s,i,l,d){var p,m;let f,h,g;(0,j.gk)({req:n},"cookies",(m=n.headers,function(){let{cookie:e}=m;if(!e)return{};let{parse:t}=r("./dist/compiled/cookie/index.js");return t(Array.isArray(e)?e.join("; "):e)}));let v={};v.assetQueryString=l.dev?l.assetQueryString||`?ts=${Date.now()}`:"",l.deploymentId&&(v.assetQueryString+=`${v.assetQueryString?"&":"?"}dpl=${l.deploymentId}`),i=Object.assign({},i);let{err:y,dev:x=!1,ampPath:b="",pageConfig:S={},buildManifest:w,reactLoadableManifest:_,ErrorDebug:P,getStaticProps:C,getStaticPaths:E,getServerSideProps:R,isDataReq:T,params:$,previewProps:N,basePath:L,images:D,runtime:F,isExperimentalCompile:q}=l,{App:U}=d,G=v.assetQueryString,Z=d.Document,Y=l.Component,J=!!i.__nextFallback,V=i.__nextNotFoundSrcPage;!function(e){for(let t of eC)delete e[t]}(i);let X=!!C,eo=X&&l.nextExport,ei=U.getInitialProps===U.origGetInitialProps,el=!!(null==Y?void 0:Y.getInitialProps),eu=null==Y?void 0:Y.unstable_scriptLoader,ep=ee(s),ef="/_error"===s&&Y.getInitialProps===Y.origGetInitialProps;l.nextExport&&el&&!ef&&t(`Detected getInitialProps on page '${s}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.
Read more: https://nextjs.org/docs/messages/get-initial-props-export`);let eg=!el&&ei&&!X&&!R;if(eg&&!x&&q&&(function(e,t){if(t.private||t.stateful)(t.private||!e.getHeader("Cache-Control"))&&e.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");else if("number"==typeof t.revalidate){if(t.revalidate<1)throw Error(`invariant: invalid Cache-Control duration provided: ${t.revalidate} < 1`);e.setHeader("Cache-Control",`s-maxage=${t.revalidate}, stale-while-revalidate`)}else!1===t.revalidate&&e.setHeader("Cache-Control","s-maxage=31536000, stale-while-revalidate")}(a,{revalidate:M.BR,private:!1,stateful:!1}),eg=!1),el&&X)throw Error(M.wh+` ${s}`);if(el&&R)throw Error(M.Wo+` ${s}`);if(R&&X)throw Error(M.oL+` ${s}`);if(R&&"export"===l.nextConfigOutput)throw Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if(E&&!ep)throw Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${s}'.
Read more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`);if(E&&!X)throw Error(`getStaticPaths was added without a getStaticProps in ${s}. Without getStaticProps, getStaticPaths does nothing`);if(X&&ep&&!E)throw Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${s}'.
Read more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`);let ev=l.resolvedAsPath||n.url;if(x){let{isValidElementType:e}=r("./dist/compiled/react-is/index.js");if(!e(Y))throw Error(`The default export is not a React Component in page: "${s}"`);if(!e(U))throw Error('The default export is not a React Component in page: "/_app"');if(!e(Z))throw Error('The default export is not a React Component in page: "/_document"');if((eg||J)&&(i={...i.amp?{amp:i.amp}:{}},ev=`${s}${n.url.endsWith("/")&&"/"!==s&&!ep?"/":""}`,n.url=s),"/404"===s&&(el||R))throw Error(`\`pages/404\` ${M.Ei}`);if(I.Er.includes(s)&&(el||R))throw Error(`\`pages${s}\` ${M.Ei}`)}for(let e of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==Y?void 0:Y[e])throw Error(`page ${s} ${e} ${M.lk}`);await Q.preloadAll(),(X||R)&&!J&&N&&(g=!1!==(f=e(n,a,N)));let ey=!!(R||el||!ei&&!X||q),e_=new eB(s,i,ev,{isFallback:J},ey,L,l.locale,l.locales,l.defaultLocale,l.domainLocales,g,ed(n,"__nextIsLocaleDomain")),eR={back(){e_.back()},forward(){e_.forward()},refresh(){e_.reload()},push(e,t){let{scroll:r}=void 0===t?{}:t;e_.push(e,void 0,{scroll:r})},replace(e,t){let{scroll:r}=void 0===t?{}:t;e_.replace(e,void 0,{scroll:r})},prefetch(e){e_.prefetch(e)}},e$={},eA=(0,k.createStyleRegistry)(),eI={ampFirst:!0===S.amp,hasQuery:!!i.amp,hybrid:"hybrid"===S.amp},eL=function(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}(eI),eD=function(e){void 0===e&&(e=!1);let t=[A().createElement("meta",{charSet:"utf-8"})];return e||t.push(A().createElement("meta",{name:"viewport",content:"width=device-width"})),t}(eL),eH=[],eZ={};eu&&(eZ.beforeInteractive=[].concat(eu()).filter(e=>"beforeInteractive"===e.props.strategy).map(e=>e.props));let eY=({children:e})=>{var t;return A().createElement(eM.Provider,{value:eR},A().createElement(eE.Provider,{value:e_.isReady&&e_.query?(t=e_.asPath,new URL(t,"http://n").searchParams):new URLSearchParams},A().createElement(ek,{router:e_,isAutoExport:eg},A().createElement(eT.Provider,{value:function(e){if(!e.isReady||!e.query)return null;let t={},r=function(e){let{parameterizedRoute:t,groups:r}=function(e){let t=(e.replace(/\/$/,"")||"/").slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=ej.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:a,repeat:s}=eO(o[1]);return r[e]={pos:n++,repeat:s,optional:a},"/"+eN(t)+"([^/]+?)"}if(!o)return"/"+eN(e);{let{key:e,repeat:t,optional:a}=eO(o[1]);return r[e]={pos:n++,repeat:t,optional:a},t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}(e.pathname),n=Object.keys(r.groups);for(let r of n)t[r]=e.query[r];return t}(e_)},A().createElement(K.Provider,{value:e_},A().createElement(z.Provider,{value:eI},A().createElement(B.Provider,{value:{updateHead:e=>{eD=e},updateScripts:e=>{e$=e},scripts:eZ,mountedInstances:new Set}},A().createElement(W.Provider,{value:e=>eH.push(e)},A().createElement(k.StyleRegistry,{registry:eA},A().createElement(ew.Provider,{value:D},e))))))))))},eJ=()=>null,eV=({children:e})=>A().createElement(A().Fragment,null,A().createElement(eJ,null),A().createElement(eY,null,A().createElement(A().Fragment,null,x?A().createElement(A().Fragment,null,e,A().createElement(eJ,null)):e,A().createElement(eJ,null)))),eQ={err:y,req:eg?void 0:n,res:eg?void 0:a,pathname:s,query:i,asPath:ev,locale:l.locale,locales:l.locales,defaultLocale:l.defaultLocale,AppTree:e=>A().createElement(eV,null,eW(U,Y,{...e,router:e_})),defaultGetInitialProps:async(e,t={})=>{let{html:r,head:n}=await e.renderPage({enhanceApp:e=>t=>A().createElement(e,t)}),o=eA.styles({nonce:t.nonce});return eA.flush(),{html:r,head:n,styles:o}}},eK=!X&&(l.nextExport||x&&(eg||J)),eX=()=>{let e=eA.styles();return eA.flush(),A().createElement(A().Fragment,null,e)};if(h=await en(U,{AppTree:eQ.AppTree,Component:Y,router:e_,ctx:eQ}),(X||R)&&g&&(h.__N_PREVIEW=!0),X&&(h[I.NO]=!0),X&&!J){let e,t;try{e=await (0,eh.getTracer)().trace(u.getStaticProps,{spanName:`getStaticProps ${s}`,attributes:{"next.route":s}},()=>C({...ep?{params:i}:void 0,...g?{draftMode:!0,preview:!0,previewData:f}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale}))}catch(e){throw e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(M.q6);let r=Object.keys(e).filter(e=>"revalidate"!==e&&"props"!==e&&"redirect"!==e&&"notFound"!==e);if(r.includes("unstable_revalidate"))throw Error(M.Eo);if(r.length)throw Error(eU("getStaticProps",r));if(void 0!==e.notFound&&void 0!==e.redirect)throw Error(`\`redirect\` and \`notFound\` can not both be returned from ${X?"getStaticProps":"getServerSideProps"} at the same time. Page: ${s}
See more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`);if("notFound"in e&&e.notFound){if("/404"===s)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');v.isNotFound=!0}if("redirect"in e&&e.redirect&&"object"==typeof e.redirect){if(eG(e.redirect,n,"getStaticProps"),eo)throw Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${n.url})
See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`);e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:ec(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0}if((x||eo)&&!v.isNotFound&&!H(s,"getStaticProps",e.props))throw Error("invariant: getStaticProps did not return valid props. Please report this.");if("revalidate"in e){if(e.revalidate&&"export"===l.nextConfigOutput)throw Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if("number"==typeof e.revalidate){if(Number.isInteger(e.revalidate)){if(e.revalidate<=0)throw Error(`A page's revalidate option can not be less than or equal to zero for ${n.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.

To never revalidate, you can set revalidate to \`false\` (only ran once at build-time).
To revalidate as soon as possible, you can set the value to \`1\`.`);e.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${n.url}. This may have been done in error.
To only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`),t=e.revalidate}else throw Error(`A page's revalidate option must be seconds expressed as a natural number for ${n.url}. Mixed numbers, such as '${e.revalidate}', cannot be used.
Try changing the value to '${Math.ceil(e.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`)}else if(!0===e.revalidate)t=1;else if(!1===e.revalidate||void 0===e.revalidate)t=!1;else throw Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(e.revalidate)}' for ${n.url}`)}else t=!1;if(h.pageProps=Object.assign({},h.pageProps,"props"in e?e.props:void 0),v.revalidate=t,v.pageData=h,v.isNotFound)return new em(null,v)}if(R&&(h[I.uY]=!0),R&&!J){let e;let r=!0,o=a,d=!1;o=new Proxy(a,{get:function(e,n){if(!r){let e=`You should not access 'res' after getServerSideProps resolves.
Read more: https://nextjs.org/docs/messages/gssp-no-mutating-res`;if(d)throw Error(e);t(e)}return eF.g.get(e,n,a)}});try{e=await (0,eh.getTracer)().trace(u.getServerSideProps,{spanName:`getServerSideProps ${s}`,attributes:{"next.route":s}},async()=>R({req:n,res:o,query:i,resolvedUrl:l.resolvedUrl,...ep?{params:$}:void 0,...!1!==f?{draftMode:!0,preview:!0,previewData:f}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale})),r=!1,v.revalidate=0}catch(e){throw"object"==typeof e&&null!==e&&"name"in e&&"message"in e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(M.Lx);e.props instanceof Promise&&(d=!0);let c=Object.keys(e).filter(e=>"props"!==e&&"redirect"!==e&&"notFound"!==e);if(e.unstable_notFound)throw Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${s}`);if(e.unstable_redirect)throw Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${s}`);if(c.length)throw Error(eU("getServerSideProps",c));if("notFound"in e&&e.notFound){if("/404"===s)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');return v.isNotFound=!0,new em(null,v)}if("redirect"in e&&"object"==typeof e.redirect&&(eG(e.redirect,n,"getServerSideProps"),e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:ec(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0),d&&(e.props=await e.props),(x||eo)&&!H(s,"getServerSideProps",e.props))throw Error("invariant: getServerSideProps did not return valid props. Please report this.");h.pageProps=Object.assign({},h.pageProps,e.props),v.pageData=h}if(!X&&!R&&Object.keys((null==h?void 0:h.pageProps)||{}).includes("url")&&console.warn(`The prop \`url\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${s}
See more info here: https://nextjs.org/docs/messages/reserved-page-prop`),T&&!X||v.isRedirect)return new em(JSON.stringify(h),v);if(J&&(h.pageProps={}),er(a)&&!X)return new em(null,v);let e0=w;if(eg&&ep){let e;let t=(e=(function(e){let t=/^\/index(\/|$)/.test(e)&&!ee(e)?"/index"+e:"/"===e?"/index":e.startsWith("/")?e:"/"+e;{let{posix:e}=r("path"),n=e.normalize(t);if(n!==t)throw new ea("Requested and resolved page mismatch: "+t+" "+n)}return t})(s).replace(/\\/g,"/")).startsWith("/index/")&&!ee(e)?e.slice(6):"/index"!==e?e:"/";t in e0.pages&&(e0={...e0,pages:{...e0.pages,[t]:[...e0.pages[t],...e0.lowPriorityFiles.filter(e=>e.includes("_buildManifest"))]},lowPriorityFiles:e0.lowPriorityFiles.filter(e=>!e.includes("_buildManifest"))})}let e1=({children:e})=>eL?e:A().createElement("div",{id:"__next"},e),e3=async()=>{let e,t,r;async function n(e){let t=async(t={})=>{if(eQ.err&&P){e&&e(U,Y);let t=await ez(A().createElement(e1,null,A().createElement(P,{error:eQ.err})));return{html:t,head:eD}}if(x&&(h.router||h.Component))throw Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props");let{App:r,Component:n}="function"==typeof t?{App:U,Component:t(Y)}:{App:t.enhanceApp?t.enhanceApp(U):U,Component:t.enhanceComponent?t.enhanceComponent(Y):Y};if(e)return e(r,n).then(async e=>{await e.allReady;let t=await eb(e);return{html:t,head:eD}});let o=await ez(A().createElement(e1,null,A().createElement(eV,null,eW(r,n,{...h,router:e_}))));return{html:o,head:eD}},r={...eQ,renderPage:t},n=await en(Z,r);if(er(a)&&!X)return null;if(!n||"string"!=typeof n.html){let e=`"${et(Z)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`;throw Error(e)}return{docProps:n,documentCtx:r}}Z[I.wU];let o=(e,t)=>{let r=e||U,n=t||Y;return eQ.err&&P?A().createElement(e1,null,A().createElement(P,{error:eQ.err})):A().createElement(e1,null,A().createElement(eV,null,eW(r,n,{...h,router:e_})))},s=async(e,t)=>{let r=o(e,t);return await function({ReactDOMServer:e,element:t,streamOptions:r}){return(0,eh.getTracer)().trace(c.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}({ReactDOMServer:O(),element:r})},i=(0,eh.getTracer)().wrap(u.createBodyResult,(e,t)=>{let r=async()=>ez(eX());return eS(e,{suffix:t,inlinedDataStream:void 0,generateStaticHTML:!0,getServerInsertedHTML:r,serverInsertedHTMLToHead:!1})}),l=!!Z.getInitialProps;if(l){if(null===(t=await n(s)))return null;let{docProps:r}=t;e=e=>i(ex(r.html+e))}else{let r=await s(U,Y);e=e=>i(r,e),t={}}let{docProps:d}=t||{};return l?(r=d.styles,eD=d.head):(r=eA.styles(),eA.flush()),{bodyResult:e,documentElement:e=>A().createElement(Z,{...e,...d}),head:eD,headTags:[],styles:r}};null==(p=(0,eh.getTracer)().getRootSpanAttributes())||p.set("next.route",l.page);let e4=await (0,eh.getTracer)().trace(u.renderDocument,{spanName:`render route (pages) ${l.page}`,attributes:{"next.route":l.page}},async()=>e3());if(!e4)return new em(null,v);let e2=new Set,e9=new Set;for(let e of eH){let t=_[e];t&&(e2.add(t.id),t.files.forEach(e=>{e9.add(e)}))}let e8=eI.hybrid,e6={},{assetPrefix:e5,buildId:e7,customServer:te,defaultLocale:tt,disableOptimizedLoading:tr,domainLocales:tn,locale:to,locales:ta,runtimeConfig:ts}=l,ti={__NEXT_DATA__:{props:h,page:s,query:i,buildId:e7,assetPrefix:""===e5?void 0:e5,runtimeConfig:ts,nextExport:!0===eK||void 0,autoExport:!0===eg||void 0,isFallback:J,isExperimentalCompile:q,dynamicIds:0===e2.size?void 0:Array.from(e2),err:l.err?function(e,t){if(e){let e;return e="server",e=r("next/dist/compiled/@next/react-dev-overlay/dist/middleware").getErrorSource(t)||"server",{name:t.name,source:e,message:eP()(t.message),stack:t.stack,digest:t.digest}}return{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}}(x,l.err):void 0,gsp:!!C||void 0,gssp:!!R||void 0,customServer:te,gip:!!el||void 0,appGip:!ei||void 0,locale:to,locales:ta,defaultLocale:tt,domainLocales:tn,isPreview:!0===g||void 0,notFoundSrcPage:V&&x?V:void 0},strictNextHead:l.strictNextHead,buildManifest:e0,docComponentsRendered:e6,dangerousAsPath:e_.asPath,canonicalBase:!l.ampPath&&ed(n,"__nextStrippedLocale")?`${l.canonicalBase||""}/${l.locale}`:l.canonicalBase,ampPath:b,inAmpMode:eL,isDevelopment:!!x,hybridAmp:e8,dynamicImports:Array.from(e9),assetPrefix:e5,unstable_runtimeJS:void 0,unstable_JsPreload:S.unstable_JsPreload,assetQueryString:G,scriptLoader:e$,locale:to,disableOptimizedLoading:tr,head:e4.head,headTags:e4.headTags,styles:e4.styles,crossOrigin:l.crossOrigin,optimizeCss:l.optimizeCss,optimizeFonts:l.optimizeFonts,nextConfigOutput:l.nextConfigOutput,nextScriptWorkers:l.nextScriptWorkers,runtime:F,largePageDataBytes:l.largePageDataBytes,nextFontManifest:l.nextFontManifest},tl=A().createElement(z.Provider,{value:eI},A().createElement(es.Provider,{value:ti},e4.documentElement(ti))),td=await (0,eh.getTracer)().trace(u.renderToString,async()=>ez(tl));{let e=[];for(let t of["Main","Head","NextScript","Html"])e6[t]||e.push(t);if(e.length){let t=e.map(e=>`<${e} />`).join(", "),r=1!==e.length?"s":"";console.warn(`Your custom Document (pages/_document) did not render all the required subcomponent${r}.
Missing component${r}: ${t}
Read how to fix here: https://nextjs.org/docs/messages/missing-document-component`)}}let[tu,tc]=td.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>"),tp="";td.startsWith(eq)||(tp+=eq),tp+=tu,eL&&(tp+="<!-- __NEXT_DATA__ -->");let tm=[ex(tp),await e4.bodyResult(tc)],tf=await eb(function(e){let{readable:t,writable:r}=new TransformStream,n=Promise.resolve();for(let t=0;t<e.length;++t)n=n.then(()=>e[t].pipeTo(r,{preventClose:t+1<e.length}));return t}(tm)),th=await o(s,tf,l,{inAmpMode:eL,hybridAmp:e8});return new em(th,v)}let eY=(e,t,r,n,o)=>eZ(e,t,r,n,o,o),eJ=A().createContext(null);function eV(e){let t=(0,$.useContext)(eJ);t&&t(e)}class eQ extends T{constructor(e){super(e),this.components=e.components}render(e,t,r){return eZ(e,t,r.page,r.query,r.renderOpts,{App:this.components.App,Document:this.components.Document})}}let eK={contexts:R},eX=eQ})(),module.exports=n})();
//# sourceMappingURL=pages.runtime.dev.js.map