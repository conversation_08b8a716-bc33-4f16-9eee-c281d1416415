# ComplianceMax Policy Information Maintenance Implementation Plan

## Executive Summary

This document outlines a comprehensive implementation plan for enhancing policy information maintenance in the ComplianceMax system. The plan addresses five key areas:

1. Structured policy management system with versioning and metadata
2. Enhanced update processes for policy information
3. Improved accessibility of policy information for users
4. Compliance verification mechanisms for policy updates
5. Policy information lifecycle management approach

The implementation leverages industry best practices for policy information lifecycle management while specifically addressing the needs of FEMA Public Assistance program compliance. This plan builds upon the existing ComplianceMax infrastructure and extends it with robust policy management capabilities.

## Background

ComplianceMax is a compliance management system focused on FEMA Public Assistance programs. Policy information maintenance is critical to ensure that compliance assessments are based on current FEMA policies and guidelines. The system currently stores policy documents in a Google Drive folder with basic organization but lacks structured metadata, versioning control, and systematic update processes.

## 1. Structured Policy Management System

### 1.1 Policy Data Model

The foundation of effective policy management is a well-structured data model that captures all relevant metadata and supports versioning.

```mermaid
classDiagram
    class PolicyDocument {
        +UUID id
        +String title
        +String description
        +String document_type
        +String file_path
        +String original_filename
        +String mime_type
        +Integer file_size
        +String checksum
        +String status
        +DateTime effective_date
        +DateTime expiration_date
        +Integer version
        +UUID parent_version_id
        +UUID superseded_by_id
        +String fema_reference_id
        +String[] tags
        +String[] categories
        +String[] applicable_regions
        +String[] disaster_types
        +String authorization_level
        +DateTime created_at
        +DateTime updated_at
        +UUID created_by
        +UUID last_updated_by
    }
    
    class PolicyVersion {
        +UUID id
        +UUID policy_id
        +Integer version_number
        +String change_summary
        +String change_type
        +String[] affected_sections
        +String approval_status
        +UUID approved_by
        +DateTime approval_date
        +String checksum
        +DateTime created_at
        +UUID created_by
    }
    
    class PolicySection {
        +UUID id
        +UUID policy_id
        +String section_id
        +String title
        +String content
        +Integer order
        +UUID parent_section_id
        +DateTime last_updated
    }
    
    class PolicyRequirement {
        +UUID id
        +UUID policy_id
        +UUID section_id
        +String requirement_text
        +String requirement_type
        +Boolean is_mandatory
        +Float compliance_weight
        +String[] related_requirements
        +DateTime created_at
        +DateTime updated_at
    }
    
    class PolicyChangeLog {
        +UUID id
        +UUID policy_id
        +UUID version_id
        +String change_type
        +String description
        +String[] affected_sections
        +String[] affected_requirements
        +DateTime change_date
        +UUID changed_by
    }
    
    PolicyDocument "1" -- "many" PolicyVersion : has
    PolicyDocument "1" -- "many" PolicySection : contains
    PolicySection "1" -- "many" PolicyRequirement : defines
    PolicyDocument "1" -- "many" PolicyChangeLog : tracks
    PolicyVersion "1" -- "many" PolicyChangeLog : details
```

### 1.2 Policy Document Schema

```python
from typing import List, Optional, Dict, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from uuid import UUID, uuid4
from enum import Enum

class PolicyStatus(str, Enum):
    DRAFT = "draft"
    REVIEW = "review"
    APPROVED = "approved"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    SUPERSEDED = "superseded"

class ChangeType(str, Enum):
    MINOR = "minor"
    MAJOR = "major"
    CRITICAL = "critical"
    EDITORIAL = "editorial"

class ApprovalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"

class PolicyDocumentBase(BaseModel):
    """Base schema for policy documents."""
    title: str
    description: Optional[str] = None
    document_type: str
    file_path: str
    original_filename: str
    mime_type: Optional[str] = None
    file_size: Optional[int] = None
    effective_date: datetime
    expiration_date: Optional[datetime] = None
    fema_reference_id: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    categories: List[str] = Field(default_factory=list)
    applicable_regions: List[str] = Field(default_factory=list)
    disaster_types: List[str] = Field(default_factory=list)
    authorization_level: str = "public"
    
    @validator('expiration_date')
    def expiration_after_effective(cls, v, values):
        if v and 'effective_date' in values and v <= values['effective_date']:
            raise ValueError('expiration_date must be after effective_date')
        return v

class PolicyDocumentCreate(PolicyDocumentBase):
    """Schema for creating a new policy document."""
    status: PolicyStatus = PolicyStatus.DRAFT

class PolicyDocumentUpdate(BaseModel):
    """Schema for updating a policy document."""
    title: Optional[str] = None
    description: Optional[str] = None
    document_type: Optional[str] = None
    file_path: Optional[str] = None
    original_filename: Optional[str] = None
    mime_type: Optional[str] = None
    file_size: Optional[int] = None
    status: Optional[PolicyStatus] = None
    effective_date: Optional[datetime] = None
    expiration_date: Optional[datetime] = None
    fema_reference_id: Optional[str] = None
    tags: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    applicable_regions: Optional[List[str]] = None
    disaster_types: Optional[List[str]] = None
    authorization_level: Optional[str] = None

class PolicyDocumentResponse(PolicyDocumentBase):
    """Schema for policy document response."""
    id: UUID
    status: PolicyStatus
    version: int
    parent_version_id: Optional[UUID] = None
    superseded_by_id: Optional[UUID] = None
    checksum: str
    created_at: datetime
    updated_at: datetime
    created_by: UUID
    last_updated_by: UUID

    class Config:
        from_attributes = True

class PolicyVersionCreate(BaseModel):
    """Schema for creating a new policy version."""
    policy_id: UUID
    change_summary: str
    change_type: ChangeType
    affected_sections: List[str] = Field(default_factory=list)
    
class PolicyVersionResponse(BaseModel):
    """Schema for policy version response."""
    id: UUID
    policy_id: UUID
    version_number: int
    change_summary: str
    change_type: ChangeType
    affected_sections: List[str]
    approval_status: ApprovalStatus
    approved_by: Optional[UUID] = None
    approval_date: Optional[datetime] = None
    checksum: str
    created_at: datetime
    created_by: UUID

    class Config:
        from_attributes = True

class PolicySectionCreate(BaseModel):
    """Schema for creating a policy section."""
    policy_id: UUID
    section_id: str
    title: str
    content: str
    order: int
    parent_section_id: Optional[UUID] = None

class PolicySectionResponse(BaseModel):
    """Schema for policy section response."""
    id: UUID
    policy_id: UUID
    section_id: str
    title: str
    content: str
    order: int
    parent_section_id: Optional[UUID] = None
    last_updated: datetime

    class Config:
        from_attributes = True

class PolicyRequirementCreate(BaseModel):
    """Schema for creating a policy requirement."""
    policy_id: UUID
    section_id: UUID
    requirement_text: str
    requirement_type: str
    is_mandatory: bool = True
    compliance_weight: float = 1.0
    related_requirements: List[str] = Field(default_factory=list)

class PolicyRequirementResponse(BaseModel):
    """Schema for policy requirement response."""
    id: UUID
    policy_id: UUID
    section_id: UUID
    requirement_text: str
    requirement_type: str
    is_mandatory: bool
    compliance_weight: float
    related_requirements: List[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PolicyChangeLogCreate(BaseModel):
    """Schema for creating a policy change log entry."""
    policy_id: UUID
    version_id: UUID
    change_type: ChangeType
    description: str
    affected_sections: List[str] = Field(default_factory=list)
    affected_requirements: List[str] = Field(default_factory=list)

class PolicyChangeLogResponse(BaseModel):
    """Schema for policy change log response."""
    id: UUID
    policy_id: UUID
    version_id: UUID
    change_type: ChangeType
    description: str
    affected_sections: List[str]
    affected_requirements: List[str]
    change_date: datetime
    changed_by: UUID

    class Config:
        from_attributes = True
```

### 1.3 Database Schema (SQL)

```sql
-- Policy document table
CREATE TABLE policy_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    document_type VARCHAR(50) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100),
    file_size INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
    expiration_date TIMESTAMP WITH TIME ZONE,
    version INTEGER NOT NULL DEFAULT 1,
    parent_version_id UUID REFERENCES policy_documents(id),
    superseded_by_id UUID REFERENCES policy_documents(id),
    checksum VARCHAR(64) NOT NULL,
    fema_reference_id VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    categories TEXT[] DEFAULT '{}',
    applicable_regions TEXT[] DEFAULT '{}',
    disaster_types TEXT[] DEFAULT '{}',
    authorization_level VARCHAR(50) DEFAULT 'public',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL,
    last_updated_by UUID NOT NULL,
    CONSTRAINT check_expiration_date CHECK (expiration_date IS NULL OR expiration_date > effective_date)
);

-- Policy versions table
CREATE TABLE policy_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_id UUID NOT NULL REFERENCES policy_documents(id),
    version_number INTEGER NOT NULL,
    change_summary TEXT NOT NULL,
    change_type VARCHAR(20) NOT NULL,
    affected_sections TEXT[] DEFAULT '{}',
    approval_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    approved_by UUID,
    approval_date TIMESTAMP WITH TIME ZONE,
    checksum VARCHAR(64) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL,
    UNIQUE(policy_id, version_number)
);

-- Policy sections table
CREATE TABLE policy_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_id UUID NOT NULL REFERENCES policy_documents(id),
    section_id VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    order_num INTEGER NOT NULL,
    parent_section_id UUID REFERENCES policy_sections(id),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(policy_id, section_id)
);

-- Policy requirements table
CREATE TABLE policy_requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_id UUID NOT NULL REFERENCES policy_documents(id),
    section_id UUID NOT NULL REFERENCES policy_sections(id),
    requirement_text TEXT NOT NULL,
    requirement_type VARCHAR(50) NOT NULL,
    is_mandatory BOOLEAN NOT NULL DEFAULT TRUE,
    compliance_weight FLOAT NOT NULL DEFAULT 1.0,
    related_requirements TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Policy change log table
CREATE TABLE policy_change_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_id UUID NOT NULL REFERENCES policy_documents(id),
    version_id UUID NOT NULL REFERENCES policy_versions(id),
    change_type VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    affected_sections TEXT[] DEFAULT '{}',
    affected_requirements TEXT[] DEFAULT '{}',
    change_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    changed_by UUID NOT NULL
);

-- Indexes for performance
CREATE INDEX idx_policy_documents_status ON policy_documents(status);
CREATE INDEX idx_policy_documents_effective_date ON policy_documents(effective_date);
CREATE INDEX idx_policy_documents_fema_reference_id ON policy_documents(fema_reference_id);
CREATE INDEX idx_policy_versions_policy_id ON policy_versions(policy_id);
CREATE INDEX idx_policy_sections_policy_id ON policy_sections(policy_id);
CREATE INDEX idx_policy_requirements_policy_id ON policy_requirements(policy_id);
CREATE INDEX idx_policy_requirements_section_id ON policy_requirements(section_id);
CREATE INDEX idx_policy_change_logs_policy_id ON policy_change_logs(policy_id);
CREATE INDEX idx_policy_change_logs_version_id ON policy_change_logs(version_id);
```

### 1.4 Prisma Schema (Alternative to SQL)

```prisma
model PolicyDocument {
  id                String           @id @default(uuid())
  title             String
  description       String?
  documentType      String
  filePath          String
  originalFilename  String
  mimeType          String?
  fileSize          Int?
  status            PolicyStatus     @default(DRAFT)
  effectiveDate     DateTime
  expirationDate    DateTime?
  version           Int              @default(1)
  parentVersionId   String?
  parentVersion     PolicyDocument?  @relation("VersionHistory", fields: [parentVersionId], references: [id])
  childVersions     PolicyDocument[] @relation("VersionHistory")
  supersededById    String?
  supersededBy      PolicyDocument?  @relation("Supersession", fields: [supersededById], references: [id])
  supersedes        PolicyDocument[] @relation("Supersession")
  checksum          String
  femaReferenceId   String?
  tags              String[]
  categories        String[]
  applicableRegions String[]
  disasterTypes     String[]
  authorizationLevel String          @default("public")
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  createdBy         String
  lastUpdatedBy     String
  
  versions          PolicyVersion[]
  sections          PolicySection[]
  changeLogs        PolicyChangeLog[]

  @@index([status])
  @@index([effectiveDate])
  @@index([femaReferenceId])
}

enum PolicyStatus {
  DRAFT
  REVIEW
  APPROVED
  PUBLISHED
  ARCHIVED
  SUPERSEDED
}

enum ChangeType {
  MINOR
  MAJOR
  CRITICAL
  EDITORIAL
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

model PolicyVersion {
  id               String         @id @default(uuid())
  policyId         String
  policy           PolicyDocument @relation(fields: [policyId], references: [id])
  versionNumber    Int
  changeSummary    String
  changeType       ChangeType
  affectedSections String[]
  approvalStatus   ApprovalStatus @default(PENDING)
  approvedBy       String?
  approvalDate     DateTime?
  checksum         String
  createdAt        DateTime       @default(now())
  createdBy        String
  
  changeLogs       PolicyChangeLog[]

  @@unique([policyId, versionNumber])
  @@index([policyId])
}

model PolicySection {
  id               String         @id @default(uuid())
  policyId         String
  policy           PolicyDocument @relation(fields: [policyId], references: [id])
  sectionId        String
  title            String
  content          String
  orderNum         Int
  parentSectionId  String?
  parentSection    PolicySection? @relation("SectionHierarchy", fields: [parentSectionId], references: [id])
  childSections    PolicySection[] @relation("SectionHierarchy")
  lastUpdated      DateTime       @default(now())
  
  requirements     PolicyRequirement[]

  @@unique([policyId, sectionId])
  @@index([policyId])
}

model PolicyRequirement {
  id                  String        @id @default(uuid())
  policyId            String
  policy              PolicyDocument @relation(fields: [policyId], references: [id])
  sectionId           String
  section             PolicySection @relation(fields: [sectionId], references: [id])
  requirementText     String
  requirementType     String
  isMandatory         Boolean       @default(true)
  complianceWeight    Float         @default(1.0)
  relatedRequirements String[]
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  @@index([policyId])
  @@index([sectionId])
}

model PolicyChangeLog {
  id                   String       @id @default(uuid())
  policyId             String
  policy               PolicyDocument @relation(fields: [policyId], references: [id])
  versionId            String
  version              PolicyVersion @relation(fields: [versionId], references: [id])
  changeType           ChangeType
  description          String
  affectedSections     String[]
  affectedRequirements String[]
  changeDate           DateTime     @default(now())
  changedBy            String

  @@index([policyId])
  @@index([versionId])
}
```

## 2. Enhanced Update Processes for Policy Information

### 2.1 Policy Update Workflow

The policy update process follows a structured workflow to ensure proper review, approval, and documentation of changes.

```mermaid
stateDiagram-v2
    [*] --> Draft: Create new policy or update
    Draft --> InReview: Submit for review
    InReview --> Rejected: Reject changes
    Rejected --> Draft: Address feedback
    InReview --> Approved: Approve changes
    Approved --> Published: Publish policy
    Published --> Active: Effective date reached
    Active --> Superseded: New version published
    Active --> Archived: Expiration date reached
    Superseded --> [*]
    Archived --> [*]
```

### 2.2 Policy Update Service Implementation

```python
from datetime import datetime
from typing import List, Optional, Dict, Union
from uuid import UUID, uuid4
import hashlib
import os
import json

class PolicyUpdateService:
    def __init__(self, db_session, storage_service, notification_service):
        self.db_session = db_session
        self.storage_service = storage_service
        self.notification_service = notification_service
    
    async def create_policy(self, policy_data: dict, file_data: bytes, created_by: UUID) -> Dict:
        """Create a new policy document with initial version."""
        # Generate checksum for file integrity
        checksum = hashlib.sha256(file_data).hexdigest()
        
        # Store file in appropriate location
        file_path = await self.storage_service.store_policy_file(
            file_data, 
            policy_data["original_filename"],
            policy_data["document_type"]
        )
        
        # Create policy document record
        policy_data["file_path"] = file_path
        policy_data["checksum"] = checksum
        policy_data["created_by"] = created_by
        policy_data["last_updated_by"] = created_by
        
        policy = await self.db_session.execute(
            """
            INSERT INTO policy_documents (
                title, description, document_type, file_path, original_filename,
                mime_type, file_size, status, effective_date, expiration_date,
                fema_reference_id, tags, categories, applicable_regions, 
                disaster_types, authorization_level, checksum, created_by, last_updated_by
            ) VALUES (
                :title, :description, :document_type, :file_path, :original_filename,
                :mime_type, :file_size, :status, :effective_date, :expiration_date,
                :fema_reference_id, :tags, :categories, :applicable_regions,
                :disaster_types, :authorization_level, :checksum, :created_by, :last_updated_by
            ) RETURNING id
            """,
            policy_data
        )
        
        policy_id = policy.fetchone()[0]
        
        # Create initial version record
        version_data = {
            "policy_id": policy_id,
            "version_number": 1,
            "change_summary": "Initial version",
            "change_type": "MAJOR",
            "checksum": checksum,
            "created_by": created_by
        }
        
        version = await self.db_session.execute(
            """
            INSERT INTO policy_versions (
                policy_id, version_number, change_summary, change_type, 
                checksum, created_by
            ) VALUES (
                :policy_id, :version_number, :change_summary, :change_type,
                :checksum, :created_by
            ) RETURNING id
            """,
            version_data
        )
        
        version_id = version.fetchone()[0]
        
        # Create change log entry
        change_log_data = {
            "policy_id": policy_id,
            "version_id": version_id,
            "change_type": "MAJOR",
            "description": "Initial policy creation",
            "changed_by": created_by
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_change_logs (
                policy_id, version_id, change_type, description, changed_by
            ) VALUES (
                :policy_id, :version_id, :change_type, :description, :changed_by
            )
            """,
            change_log_data
        )
        
        await self.db_session.commit()
        
        # Notify relevant stakeholders
        await self.notification_service.notify_policy_created(policy_id)
        
        return {"policy_id": policy_id, "version_id": version_id}
    
    async def update_policy(
        self, 
        policy_id: UUID, 
        update_data: dict, 
        file_data: Optional[bytes], 
        updated_by: UUID,
        change_type: str,
        change_summary: str,
        affected_sections: List[str] = None
    ) -> Dict:
        """Update an existing policy with a new version."""
        # Get current policy data
        current_policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        current_policy = current_policy.fetchone()
        
        if not current_policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Determine new version number
        current_version = current_policy["version"]
        new_version = current_version + 1
        
        # Handle file update if provided
        if file_data:
            # Generate new checksum
            checksum = hashlib.sha256(file_data).hexdigest()
            
            # Store new file
            file_path = await self.storage_service.store_policy_file(
                file_data, 
                update_data.get("original_filename", current_policy["original_filename"]),
                update_data.get("document_type", current_policy["document_type"])
            )
            
            update_data["file_path"] = file_path
            update_data["checksum"] = checksum
        else:
            checksum = current_policy["checksum"]
        
        # Create new policy version (copy of current with updates)
        new_policy_data = dict(current_policy)
        new_policy_data.update(update_data)
        new_policy_data["version"] = new_version
        new_policy_data["parent_version_id"] = policy_id
        new_policy_data["last_updated_by"] = updated_by
        new_policy_data["updated_at"] = datetime.now()
        
        # Insert new policy version
        new_policy = await self.db_session.execute(
            """
            INSERT INTO policy_documents (
                title, description, document_type, file_path, original_filename,
                mime_type, file_size, status, effective_date, expiration_date,
                version, parent_version_id, fema_reference_id, tags, categories, 
                applicable_regions, disaster_types, authorization_level, checksum,
                created_by, last_updated_by
            ) VALUES (
                :title, :description, :document_type, :file_path, :original_filename,
                :mime_type, :file_size, :status, :effective_date, :expiration_date,
                :version, :parent_version_id, :fema_reference_id, :tags, :categories,
                :applicable_regions, :disaster_types, :authorization_level, :checksum,
                :created_by, :last_updated_by
            ) RETURNING id
            """,
            new_policy_data
        )
        
        new_policy_id = new_policy.fetchone()[0]
        
        # Update original policy to be superseded
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET superseded_by_id = :superseded_by_id, status = 'SUPERSEDED'
            WHERE id = :id
            """,
            {"superseded_by_id": new_policy_id, "id": policy_id}
        )
        
        # Create version record
        version_data = {
            "policy_id": new_policy_id,
            "version_number": new_version,
            "change_summary": change_summary,
            "change_type": change_type,
            "affected_sections": affected_sections or [],
            "checksum": checksum,
            "created_by": updated_by
        }
        
        version = await self.db_session.execute(
            """
            INSERT INTO policy_versions (
                policy_id, version_number, change_summary, change_type, 
                affected_sections, checksum, created_by
            ) VALUES (
                :policy_id, :version_number, :change_summary, :change_type,
                :affected_sections, :checksum, :created_by
            ) RETURNING id
            """,
            version_data
        )
        
        version_id = version.fetchone()[0]
        
        # Create change log entry
        change_log_data = {
            "policy_id": new_policy_id,
            "version_id": version_id,
            "change_type": change_type,
            "description": change_summary,
            "affected_sections": affected_sections or [],
            "changed_by": updated_by
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_change_logs (
                policy_id, version_id, change_type, description, 
                affected_sections, changed_by
            ) VALUES (
                :policy_id, :version_id, :change_type, :description,
                :affected_sections, :changed_by
            )
            """,
            change_log_data
        )
        
        await self.db_session.commit()
        
        # Notify relevant stakeholders
        await self.notification_service.notify_policy_updated(new_policy_id, policy_id)
        
        return {"policy_id": new_policy_id, "version_id": version_id}
    
    async def submit_for_review(self, policy_id: UUID, submitted_by: UUID) -> Dict:
        """Submit a policy for review."""
        # Update policy status
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET status = 'REVIEW', last_updated_by = :last_updated_by, updated_at = NOW()
            WHERE id = :id AND status = 'DRAFT'
            """,
            {"id": policy_id, "last_updated_by": submitted_by}
        )
        
        await self.db_session.commit()
        
        # Notify reviewers
        await self.notification_service.notify_policy_review_needed(policy_id, submitted_by)
        
        return {"policy_id": policy_id, "status": "REVIEW"}
    
    async def approve_policy(
        self, 
        policy_id: UUID, 
        version_id: UUID,
        approved_by: UUID,
        approval_notes: Optional[str] = None
    ) -> Dict:
        """Approve a policy version."""
        # Update version approval status
        await self.db_session.execute(
            """
            UPDATE policy_versions 
            SET approval_status = 'APPROVED', approved_by = :approved_by, approval_date = NOW()
            WHERE id = :id AND approval_status = 'PENDING'
            """,
            {"id": version_id, "approved_by": approved_by}
        )
        
        # Update policy status
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET status = 'APPROVED', last_updated_by = :last_updated_by, updated_at = NOW()
            WHERE id = :id AND status = 'REVIEW'
            """,
            {"id": policy_id, "last_updated_by": approved_by}
        )
        
        await self.db_session.commit()
        
        # Notify stakeholders
        await self.notification_service.notify_policy_approved(policy_id, approved_by, approval_notes)
        
        return {"policy_id": policy_id, "version_id": version_id, "status": "APPROVED"}
    
    async def publish_policy(self, policy_id: UUID, published_by: UUID) -> Dict:
        """Publish an approved policy."""
        # Update policy status
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET status = 'PUBLISHED', last_updated_by = :last_updated_by, updated_at = NOW()
            WHERE id = :id AND status = 'APPROVED'
            """,
            {"id": policy_id, "last_updated_by": published_by}
        )
        
        await self.db_session.commit()
        
        # Notify all users
        await self.notification_service.notify_policy_published(policy_id)
        
        return {"policy_id": policy_id, "status": "PUBLISHED"}
    
    async def archive_policy(self, policy_id: UUID, archived_by: UUID, reason: str) -> Dict:
        """Archive a policy."""
        # Update policy status
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET status = 'ARCHIVED', last_updated_by = :last_updated_by, updated_at = NOW()
            WHERE id = :id AND status IN ('PUBLISHED', 'SUPERSEDED')
            """,
            {"id": policy_id, "last_updated_by": archived_by}
        )
        
        # Create change log entry
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        version = await self.db_session.execute(
            """
            SELECT * FROM policy_versions 
            WHERE policy_id = :policy_id 
            ORDER BY version_number DESC LIMIT 1
            """,
            {"policy_id": policy_id}
        )
        version = version.fetchone()
        
        change_log_data = {
            "policy_id": policy_id,
            "version_id": version["id"],
            "change_type": "EDITORIAL",
            "description": f"Policy archived: {reason}",
            "changed_by": archived_by
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_change_logs (
                policy_id, version_id, change_type, description, changed_by
            ) VALUES (
                :policy_id, :version_id, :change_type, :description, :changed_by
            )
            """,
            change_log_data
        )
        
        await self.db_session.commit()
        
        # Notify stakeholders
        await self.notification_service.notify_policy_archived(policy_id, reason)
        
        return {"policy_id": policy_id, "status": "ARCHIVED"}
```

### 2.3 CI/CD Pipeline for Policy Validation

```yaml
# .github/workflows/policy-validation.yml
name: Policy Validation

on:
  pull_request:
    paths:
      - 'policies/**'
      - 'app/schemas/policy*.py'
  workflow_dispatch:

jobs:
  validate-policies:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Validate policy schemas
        run: |
          python -m app.validation.policy_schema_validator
          
      - name: Check policy metadata consistency
        run: |
          python -m app.validation.policy_metadata_validator
          
      - name: Verify policy references
        run: |
          python -m app.validation.policy_reference_validator
          
      - name: Generate policy diff report
        if: github.event_name == 'pull_request'
        run: |
          python -m app.tools.policy_diff_generator
          
      - name: Upload validation report
        uses: actions/upload-artifact@v3
        with:
          name: policy-validation-report
          path: reports/policy-validation-report.md
```

### 2.4 Policy Validation Script

```python
# app/validation/policy_schema_validator.py
import json
import os
import sys
from jsonschema import validate, ValidationError
from datetime import datetime
import hashlib

def load_schema(schema_path):
    """Load JSON schema from file."""
    with open(schema_path, 'r') as f:
        return json.load(f)

def validate_policy_file(policy_path, schema):
    """Validate a policy file against the schema."""
    try:
        with open(policy_path, 'r') as f:
            policy_data = json.load(f)
        
        validate(instance=policy_data, schema=schema)
        
        # Additional validation checks
        if 'effectiveDate' in policy_data:
            effective_date = datetime.fromisoformat(policy_data['effectiveDate'].replace('Z', '+00:00'))
            if effective_date < datetime.now():
                print(f"Warning: Policy {policy_path} has an effective date in the past")
        
        if 'expirationDate' in policy_data and policy_data['expirationDate']:
            expiration_date = datetime.fromisoformat(policy_data['expirationDate'].replace('Z', '+00:00'))
            effective_date = datetime.fromisoformat(policy_data['effectiveDate'].replace('Z', '+00:00'))
            if expiration_date <= effective_date:
                print(f"Error: Policy {policy_path} has expiration date before or equal to effective date")
                return False
        
        # Verify checksum if present
        if 'checksum' in policy_data and os.path.exists(policy_data.get('filePath', '')):
            with open(policy_data['filePath'], 'rb') as f:
                file_data = f.read()
                calculated_checksum = hashlib.sha256(file_data).hexdigest()
                if calculated_checksum != policy_data['checksum']:
                    print(f"Error: Checksum mismatch for {policy_path}")
                    return False
        
        return True
    except ValidationError as e:
        print(f"Validation error in {policy_path}: {e}")
        return False
    except Exception as e:
        print(f"Error processing {policy_path}: {e}")
        return False

def main():
    """Main validation function."""
    schema_path = 'app/schemas/policy_document_schema.json'
    policies_dir = 'policies'
    
    if not os.path.exists(schema_path):
        print(f"Error: Schema file not found at {schema_path}")
        sys.exit(1)
    
    if not os.path.exists(policies_dir):
        print(f"Error: Policies directory not found at {policies_dir}")
        sys.exit(1)
    
    schema = load_schema(schema_path)
    
    validation_errors = 0
    validated_files = 0
    
    for root, _, files in os.walk(policies_dir):
        for file in files:
            if file.endswith('.json'):
                policy_path = os.path.join(root, file)
                if not validate_policy_file(policy_path, schema):
                    validation_errors += 1
                validated_files += 1
    
    print(f"Validation complete. Processed {validated_files} files with {validation_errors} errors.")
    
    if validation_errors > 0:
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 3. Improved Accessibility of Policy Information

### 3.1 Policy API Endpoints

```python
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File
from typing import List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.policy import (
    PolicyDocumentCreate, PolicyDocumentUpdate, PolicyDocumentResponse,
    PolicyVersionCreate, PolicyVersionResponse,
    PolicySectionCreate, PolicySectionResponse,
    PolicyRequirementCreate, PolicyRequirementResponse
)
from app.services.policy import PolicyService
from app.db.session import get_db
from app.auth.dependencies import get_current_user

router = APIRouter(prefix="/api/policies", tags=["policies"])

@router.post("/", response_model=PolicyDocumentResponse)
async def create_policy(
    policy_data: PolicyDocumentCreate,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new policy document."""
    policy_service = PolicyService(db)
    file_content = await file.read()
    return await policy_service.create_policy(policy_data, file_content, current_user.id)

@router.get("/", response_model=List[PolicyDocumentResponse])
async def list_policies(
    status: Optional[str] = Query(None, description="Filter by status"),
    category: Optional[str] = Query(None, description="Filter by category"),
    effective_before: Optional[str] = Query(None, description="Filter by effective date before"),
    effective_after: Optional[str] = Query(None, description="Filter by effective date after"),
    search: Optional[str] = Query(None, description="Search in title and description"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """List policy documents with filtering options."""
    policy_service = PolicyService(db)
    return await policy_service.list_policies(
        status=status,
        category=category,
        effective_before=effective_before,
        effective_after=effective_after,
        search=search,
        skip=skip,
        limit=limit
    )

@router.get("/{policy_id}", response_model=PolicyDocumentResponse)
async def get_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to retrieve"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get a specific policy document by ID."""
    policy_service = PolicyService(db)
    policy = await policy_service.get_policy(policy_id)
    if not policy:
        raise HTTPException(status_code=404, detail="Policy not found")
    return policy

@router.put("/{policy_id}", response_model=PolicyDocumentResponse)
async def update_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to update"),
    policy_data: PolicyDocumentUpdate = None,
    file: Optional[UploadFile] = File(None),
    change_type: str = Query(..., description="Type of change (MINOR, MAJOR, CRITICAL, EDITORIAL)"),
    change_summary: str = Query(..., description="Summary of changes made"),
    affected_sections: List[str] = Query(None, description="List of affected section IDs"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Update an existing policy document."""
    policy_service = PolicyService(db)
    file_content = await file.read() if file else None
    return await policy_service.update_policy(
        policy_id, 
        policy_data, 
        file_content, 
        current_user.id,
        change_type,
        change_summary,
        affected_sections
    )

@router.post("/{policy_id}/review", response_model=dict)
async def submit_for_review(
    policy_id: UUID = Path(..., description="The ID of the policy to submit for review"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Submit a policy for review."""
    policy_service = PolicyService(db)
    return await policy_service.submit_for_review(policy_id, current_user.id)

@router.post("/{policy_id}/approve", response_model=dict)
async def approve_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to approve"),
    version_id: UUID = Query(..., description="The ID of the version to approve"),
    approval_notes: Optional[str] = Query(None, description="Notes regarding the approval"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Approve a policy version."""
    policy_service = PolicyService(db)
    return await policy_service.approve_policy(policy_id, version_id, current_user.id, approval_notes)

@router.post("/{policy_id}/publish", response_model=dict)
async def publish_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to publish"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Publish an approved policy."""
    policy_service = PolicyService(db)
    return await policy_service.publish_policy(policy_id, current_user.id)

@router.post("/{policy_id}/archive", response_model=dict)
async def archive_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to archive"),
    reason: str = Query(..., description="Reason for archiving"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Archive a policy."""
    policy_service = PolicyService(db)
    return await policy_service.archive_policy(policy_id, current_user.id, reason)

@router.get("/{policy_id}/versions", response_model=List[PolicyVersionResponse])
async def list_policy_versions(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """List all versions of a policy."""
    policy_service = PolicyService(db)
    return await policy_service.list_policy_versions(policy_id)

@router.get("/{policy_id}/versions/{version_number}", response_model=PolicyDocumentResponse)
async def get_policy_version(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    version_number: int = Path(..., description="The version number to retrieve"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get a specific version of a policy."""
    policy_service = PolicyService(db)
    policy = await policy_service.get_policy_version(policy_id, version_number)
    if not policy:
        raise HTTPException(status_code=404, detail="Policy version not found")
    return policy

@router.get("/{policy_id}/sections", response_model=List[PolicySectionResponse])
async def list_policy_sections(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """List all sections of a policy."""
    policy_service = PolicyService(db)
    return await policy_service.list_policy_sections(policy_id)

@router.post("/{policy_id}/sections", response_model=PolicySectionResponse)
async def create_policy_section(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    section_data: PolicySectionCreate = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new section for a policy."""
    policy_service = PolicyService(db)
    return await policy_service.create_policy_section(policy_id, section_data, current_user.id)

@router.get("/{policy_id}/requirements", response_model=List[PolicyRequirementResponse])
async def list_policy_requirements(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """List all requirements of a policy."""
    policy_service = PolicyService(db)
    return await policy_service.list_policy_requirements(policy_id)

@router.post("/{policy_id}/requirements", response_model=PolicyRequirementResponse)
async def create_policy_requirement(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    requirement_data: PolicyRequirementCreate = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Create a new requirement for a policy."""
    policy_service = PolicyService(db)
    return await policy_service.create_policy_requirement(policy_id, requirement_data, current_user.id)

@router.get("/search", response_model=List[PolicyDocumentResponse])
async def search_policies(
    query: str = Query(..., description="Search query string"),
    include_content: bool = Query(False, description="Include full content in search"),
    status: Optional[str] = Query(None, description="Filter by status"),
    category: Optional[str] = Query(None, description="Filter by category"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Search for policies based on query string."""
    policy_service = PolicyService(db)
    return await policy_service.search_policies(
        query=query,
        include_content=include_content,
        status=status,
        category=category,
        skip=skip,
        limit=limit
    )
```

### 3.2 Policy Search and Filtering Component (React)

```jsx
// components/PolicySearch.jsx
import React, { useState, useEffect } from 'react';
import { 
  Box, TextField, Button, Select, MenuItem, FormControl, 
  InputLabel, Chip, Typography, Grid, Card, CardContent, 
  CardActions, IconButton, Tooltip, CircularProgress 
} from '@mui/material';
import { 
  Search as SearchIcon, 
  FilterList as FilterIcon,
  GetApp as DownloadIcon,
  History as HistoryIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers';
import { useNavigate } from 'react-router-dom';
import { usePolicySearch } from '../hooks/usePolicySearch';

const PolicySearch = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useState({
    query: '',
    status: '',
    category: '',
    effectiveAfter: null,
    effectiveBefore: null,
    includeContent: true,
    skip: 0,
    limit: 10
  });
  
  const [showFilters, setShowFilters] = useState(false);
  
  const { 
    policies, 
    loading, 
    error, 
    totalCount,
    fetchPolicies,
    categories
  } = usePolicySearch();
  
  useEffect(() => {
    fetchPolicies(searchParams);
  }, [searchParams.skip, searchParams.limit]);
  
  const handleSearch = () => {
    setSearchParams({
      ...searchParams,
      skip: 0 // Reset pagination when searching
    });
    fetchPolicies({
      ...searchParams,
      skip: 0
    });
  };
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams({
      ...searchParams,
      [name]: value
    });
  };
  
  const handleDateChange = (name, date) => {
    setSearchParams({
      ...searchParams,
      [name]: date
    });
  };
  
  const handleViewPolicy = (policyId) => {
    navigate(`/policies/${policyId}`);
  };
  
  const handleViewVersionHistory = (policyId) => {
    navigate(`/policies/${policyId}/versions`);
  };
  
  const handleDownloadPolicy = (policyId, filename) => {
    // Download policy file
    fetch(`/api/policies/${policyId}/download`)
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
      });
  };
  
  const handlePageChange = (newPage) => {
    setSearchParams({
      ...searchParams,
      skip: newPage * searchParams.limit
    });
  };
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Policy Search
      </Typography>
      
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'flex-end' }}>
        <TextField
          name="query"
          label="Search Policies"
          variant="outlined"
          fullWidth
          value={searchParams.query}
          onChange={handleInputChange}
          sx={{ mr: 1 }}
        />
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<SearchIcon />}
          onClick={handleSearch}
        >
          Search
        </Button>
        <Button
          variant="outlined"
          color="secondary"
          startIcon={<FilterIcon />}
          onClick={() => setShowFilters(!showFilters)}
          sx={{ ml: 1 }}
        >
          Filters
        </Button>
      </Box>
      
      {showFilters && (
        <Box sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={searchParams.status}
                  onChange={handleInputChange}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="DRAFT">Draft</MenuItem>
                  <MenuItem value="REVIEW">In Review</MenuItem>
                  <MenuItem value="APPROVED">Approved</MenuItem>
                  <MenuItem value="PUBLISHED">Published</MenuItem>
                  <MenuItem value="ARCHIVED">Archived</MenuItem>
                  <MenuItem value="SUPERSEDED">Superseded</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={searchParams.category}
                  onChange={handleInputChange}
                  label="Category"
                >
                  <MenuItem value="">All</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="Effective After"
                value={searchParams.effectiveAfter}
                onChange={(date) => handleDateChange('effectiveAfter', date)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="Effective Before"
                value={searchParams.effectiveBefore}
                onChange={(date) => handleDateChange('effectiveBefore', date)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
          </Grid>
        </Box>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Typography color="error" sx={{ p: 2 }}>
          Error loading policies: {error}
        </Typography>
      ) : (
        <>
          <Typography variant="subtitle1" gutterBottom>
            {totalCount} policies found
          </Typography>
          
          <Grid container spacing={2}>
            {policies.map(policy => (
              <Grid item xs={12} key={policy.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="h6">{policy.title}</Typography>
                      <Chip 
                        label={policy.status} 
                        color={
                          policy.status === 'PUBLISHED' ? 'success' :
                          policy.status === 'DRAFT' ? 'default' :
                          policy.status === 'REVIEW' ? 'info' :
                          policy.status === 'APPROVED' ? 'primary' :
                          'secondary'
                        }
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {policy.description}
                    </Typography>
                    
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" display="block">
                        Effective: {new Date(policy.effectiveDate).toLocaleDateString()}
                        {policy.expirationDate && ` - ${new Date(policy.expirationDate).toLocaleDateString()}`}
                      </Typography>
                      
                      <Typography variant="caption" display="block">
                        Version: {policy.version}
                      </Typography>
                      
                      <Box sx={{ mt: 1 }}>
                        {policy.tags.map(tag => (
                          <Chip 
                            key={tag} 
                            label={tag} 
                            size="small" 
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                      </Box>
                    </Box>
                  </CardContent>
                  
                  <CardActions>
                    <Tooltip title="View Policy">
                      <IconButton onClick={() => handleViewPolicy(policy.id)}>
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Version History">
                      <IconButton onClick={() => handleViewVersionHistory(policy.id)}>
                        <HistoryIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Download">
                      <IconButton onClick={() => handleDownloadPolicy(policy.id, policy.originalFilename)}>
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
          
          {/* Pagination controls */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            {/* Pagination component here */}
          </Box>
        </>
      )}
    </Box>
  );
};

export default PolicySearch;
```

### 3.3 Policy Detail View Component (React)

```jsx
// components/PolicyDetail.jsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Box, Typography, Paper, Divider, Chip, Button, 
  Tabs, Tab, CircularProgress, Alert, Grid, 
  List, ListItem, ListItemText, IconButton, Tooltip 
} from '@mui/material';
import { 
  GetApp as DownloadIcon,
  History as HistoryIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as ApproveIcon,
  Send as SubmitIcon,
  Publish as PublishIcon,
  Archive as ArchiveIcon
} from '@mui/icons-material';
import { usePolicyDetail } from '../hooks/usePolicyDetail';
import { useAuth } from '../hooks/useAuth';
import PolicyRequirementsList from './PolicyRequirementsList';
import PolicySectionsList from './PolicySectionsList';
import PolicyChangeLog from './PolicyChangeLog';
import ConfirmationDialog from './ConfirmationDialog';

const PolicyDetail = () => {
  const { policyId } = useParams();
  const navigate = useNavigate();
  const { user, hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    action: null
  });
  
  const { 
    policy, 
    loading, 
    error, 
    sections,
    requirements,
    changeLogs,
    fetchPolicy,
    submitForReview,
    approvePolicy,
    publishPolicy,
    archivePolicy
  } = usePolicyDetail(policyId);
  
  useEffect(() => {
    fetchPolicy();
  }, [policyId]);
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const handleDownload = () => {
    // Download policy file
    fetch(`/api/policies/${policyId}/download`)
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = policy.originalFilename;
        document.body.appendChild(a);
        a.click();
        a.remove();
      });
  };
  
  const handleEdit = () => {
    navigate(`/policies/${policyId}/edit`);
  };
  
  const handleVersionHistory = () => {
    navigate(`/policies/${policyId}/versions`);
  };
  
  const handleSubmitForReview = () => {
    setConfirmDialog({
      open: true,
      title: 'Submit for Review',
      message: 'Are you sure you want to submit this policy for review? This will change its status from Draft to Review.',
      action: () => {
        submitForReview().then(() => {
          fetchPolicy();
        });
      }
    });
  };
  
  const handleApprove = () => {
    setConfirmDialog({
      open: true,
      title: 'Approve Policy',
      message: 'Are you sure you want to approve this policy? This will change its status from Review to Approved.',
      action: () => {
        approvePolicy().then(() => {
          fetchPolicy();
        });
      }
    });
  };
  
  const handlePublish = () => {
    setConfirmDialog({
      open: true,
      title: 'Publish Policy',
      message: 'Are you sure you want to publish this policy? This will make it available to all users.',
      action: () => {
        publishPolicy().then(() => {
          fetchPolicy();
        });
      }
    });
  };
  
  const handleArchive = () => {
    setConfirmDialog({
      open: true,
      title: 'Archive Policy',
      message: 'Are you sure you want to archive this policy? This will make it unavailable for new compliance checks.',
      action: () => {
        archivePolicy().then(() => {
          fetchPolicy();
        });
      }
    });
  };
  
  const closeConfirmDialog = () => {
    setConfirmDialog({
      ...confirmDialog,
      open: false
    });
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading policy: {error}
      </Alert>
    );
  }
  
  if (!policy) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        Policy not found
      </Alert>
    );
  }
  
  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              {policy.title}
            </Typography>
            <Chip 
              label={policy.status} 
              color={
                policy.status === 'PUBLISHED' ? 'success' :
                policy.status === 'DRAFT' ? 'default' :
                policy.status === 'REVIEW' ? 'info' :
                policy.status === 'APPROVED' ? 'primary' :
                'secondary'
              }
              sx={{ mr: 1 }}
            />
            <Typography variant="caption">
              Version: {policy.version}
            </Typography>
          </Box>
          
          <Box>
            {/* Action buttons based on policy status and user permissions */}
            {policy.status === 'DRAFT' && hasPermission('policy:edit') && (
              <Button 
                variant="contained" 
                color="primary" 
                startIcon={<EditIcon />}
                onClick={handleEdit}
                sx={{ mr: 1 }}
              >
                Edit
              </Button>
            )}
            
            {policy.status === 'DRAFT' && hasPermission('policy:submit_review') && (
              <Button 
                variant="contained" 
                color="secondary" 
                startIcon={<SubmitIcon />}
                onClick={handleSubmitForReview}
                sx={{ mr: 1 }}
              >
                Submit for Review
              </Button>
            )}
            
            {policy.status === 'REVIEW' && hasPermission('policy:approve') && (
              <Button 
                variant="contained" 
                color="success" 
                startIcon={<ApproveIcon />}
                onClick={handleApprove}
                sx={{ mr: 1 }}
              >
                Approve
              </Button>
            )}
            
            {policy.status === 'APPROVED' && hasPermission('policy:publish') && (
              <Button 
                variant="contained" 
                color="primary" 
                startIcon={<PublishIcon />}
                onClick={handlePublish}
                sx={{ mr: 1 }}
              >
                Publish
              </Button>
            )}
            
            {(policy.status === 'PUBLISHED' || policy.status === 'SUPERSEDED') && hasPermission('policy:archive') && (
              <Button 
                variant="outlined" 
                color="warning" 
                startIcon={<ArchiveIcon />}
                onClick={handleArchive}
                sx={{ mr: 1 }}
              >
                Archive
              </Button>
            )}
            
            <Button 
              variant="outlined" 
              startIcon={<DownloadIcon />}
              onClick={handleDownload}
              sx={{ mr: 1 }}
            >
              Download
            </Button>
            
            <Button 
              variant="outlined" 
              startIcon={<HistoryIcon />}
              onClick={handleVersionHistory}
            >
              History
            </Button>
          </Box>
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1">Description</Typography>
            <Typography variant="body1" paragraph>
              {policy.description || 'No description provided'}
            </Typography>
            
            <Typography variant="subtitle1">Document Type</Typography>
            <Typography variant="body1" paragraph>
              {policy.documentType}
            </Typography>
            
            <Typography variant="subtitle1">FEMA Reference ID</Typography>
            <Typography variant="body1" paragraph>
              {policy.femaReferenceId || 'N/A'}
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1">Effective Date</Typography>
            <Typography variant="body1" paragraph>
              {new Date(policy.effectiveDate).toLocaleDateString()}
            </Typography>
            
            {policy.expirationDate && (
              <>
                <Typography variant="subtitle1">Expiration Date</Typography>
                <Typography variant="body1" paragraph>
                  {new Date(policy.expirationDate).toLocaleDateString()}
                </Typography>
              </>
            )}
            
            <Typography variant="subtitle1">Categories</Typography>
            <Box sx={{ mb: 2 }}>
              {policy.categories.length > 0 ? (
                policy.categories.map(category => (
                  <Chip 
                    key={category} 
                    label={category} 
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))
              ) : (
                <Typography variant="body2">No categories</Typography>
              )}
            </Box>
            
            <Typography variant="subtitle1">Tags</Typography>
            <Box>
              {policy.tags.length > 0 ? (
                policy.tags.map(tag => (
                  <Chip 
                    key={tag} 
                    label={tag} 
                    size="small"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))
              ) : (
                <Typography variant="body2">No tags</Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      <Box sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Sections" />
          <Tab label="Requirements" />
          <Tab label="Change Log" />
          <Tab label="Metadata" />
        </Tabs>
        
        <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderTop: 0 }}>
          {activeTab === 0 && (
            <PolicySectionsList 
              sections={sections} 
              policyId={policyId} 
              canEdit={policy.status === 'DRAFT' && hasPermission('policy:edit')}
            />
          )}
          
          {activeTab === 1 && (
            <PolicyRequirementsList 
              requirements={requirements} 
              policyId={policyId}
              canEdit={policy.status === 'DRAFT' && hasPermission('policy:edit')}
            />
          )}
          
          {activeTab === 2 && (
            <PolicyChangeLog 
              changeLogs={changeLogs} 
              policyId={policyId}
            />
          )}
          
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Policy Metadata
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemText 
                    primary="File Name" 
                    secondary={policy.originalFilename} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemText 
                    primary="MIME Type" 
                    secondary={policy.mimeType} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemText 
                    primary="File Size" 
                    secondary={`${Math.round(policy.fileSize / 1024)} KB`} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemText 
                    primary="Checksum" 
                    secondary={policy.checksum} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemText 
                    primary="Created At" 
                    secondary={new Date(policy.createdAt).toLocaleString()} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemText 
                    primary="Last Updated" 
                    secondary={new Date(policy.updatedAt).toLocaleString()} 
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemText 
                    primary="Authorization Level" 
                    secondary={policy.authorizationLevel} 
                  />
                </ListItem>
                
                {policy.applicableRegions.length > 0 && (
                  <ListItem>
                    <ListItemText 
                      primary="Applicable Regions" 
                      secondary={policy.applicableRegions.join(', ')} 
                    />
                  </ListItem>
                )}
                
                {policy.disasterTypes.length > 0 && (
                  <ListItem>
                    <ListItemText 
                      primary="Disaster Types" 
                      secondary={policy.disasterTypes.join(', ')} 
                    />
                  </ListItem>
                )}
              </List>
            </Box>
          )}
        </Box>
      </Box>
      
      <ConfirmationDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={() => {
          confirmDialog.action();
          closeConfirmDialog();
        }}
        onCancel={closeConfirmDialog}
      />
    </Box>
  );
};

export default PolicyDetail;
```

## 4. Compliance Verification Mechanisms

### 4.1 Policy Verification Service

```python
import hashlib
import json
import os
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from uuid import UUID

class PolicyVerificationService:
    def __init__(self, db_session, storage_service, config):
        self.db_session = db_session
        self.storage_service = storage_service
        self.config = config
        self.fema_api_url = config.get("FEMA_API_URL")
        self.fema_api_key = config.get("FEMA_API_KEY")
    
    async def verify_policy_integrity(self, policy_id: UUID) -> Dict:
        """Verify the integrity of a policy document by checking its checksum."""
        # Get policy data
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Get file data
        file_path = policy["file_path"]
        stored_checksum = policy["checksum"]
        
        # Calculate checksum from file
        file_data = await self.storage_service.get_file_content(file_path)
        calculated_checksum = hashlib.sha256(file_data).hexdigest()
        
        # Compare checksums
        is_valid = calculated_checksum == stored_checksum
        
        # Log verification result
        verification_log = {
            "policy_id": policy_id,
            "verification_type": "integrity",
            "is_valid": is_valid,
            "details": {
                "stored_checksum": stored_checksum,
                "calculated_checksum": calculated_checksum
            },
            "verified_at": datetime.now(),
            "verified_by": "system"
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_verification_logs (
                policy_id, verification_type, is_valid, details, verified_at, verified_by
            ) VALUES (
                :policy_id, :verification_type, :is_valid, :details, :verified_at, :verified_by
            )
            """,
            verification_log
        )
        
        await self.db_session.commit()
        
        return {
            "policy_id": policy_id,
            "is_valid": is_valid,
            "verification_type": "integrity",
            "details": {
                "stored_checksum": stored_checksum,
                "calculated_checksum": calculated_checksum
            }
        }
    
    async def verify_policy_metadata(self, policy_id: UUID) -> Dict:
        """Verify policy metadata for completeness and consistency."""
        # Get policy data
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Define required fields
        required_fields = [
            "title", "document_type", "file_path", "original_filename",
            "status", "effective_date", "checksum"
        ]
        
        # Check for missing required fields
        missing_fields = [field for field in required_fields if not policy.get(field)]
        
        # Check date consistency
        date_consistent = True
        date_issues = []
        
        if policy.get("expiration_date") and policy.get("effective_date"):
            if policy["expiration_date"] <= policy["effective_date"]:
                date_consistent = False
                date_issues.append("Expiration date must be after effective date")
        
        # Check if effective date is in the future for draft policies
        if policy.get("status") == "DRAFT" and policy.get("effective_date"):
            if policy["effective_date"] < datetime.now():
                date_issues.append("Draft policy has effective date in the past")
        
        # Verify FEMA reference ID if provided
        fema_reference_valid = None
        fema_reference_details = None
        
        if policy.get("fema_reference_id") and self.fema_api_url:
            fema_reference_valid, fema_reference_details = await self._verify_fema_reference(
                policy["fema_reference_id"]
            )
        
        # Determine overall validity
        is_valid = (
            len(missing_fields) == 0 and
            date_consistent and
            (fema_reference_valid is None or fema_reference_valid)
        )
        
        # Compile verification details
        details = {
            "missing_fields": missing_fields,
            "date_issues": date_issues
        }
        
        if fema_reference_details:
            details["fema_reference"] = fema_reference_details
        
        # Log verification result
        verification_log = {
            "policy_id": policy_id,
            "verification_type": "metadata",
            "is_valid": is_valid,
            "details": json.dumps(details),
            "verified_at": datetime.now(),
            "verified_by": "system"
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_verification_logs (
                policy_id, verification_type, is_valid, details, verified_at, verified_by
            ) VALUES (
                :policy_id, :verification_type, :is_valid, :details, :verified_at, :verified_by
            )
            """,
            verification_log
        )
        
        await self.db_session.commit()
        
        return {
            "policy_id": policy_id,
            "is_valid": is_valid,
            "verification_type": "metadata",
            "details": details
        }
    
    async def verify_policy_content(self, policy_id: UUID) -> Dict:
        """Verify policy content for compliance with standards."""
        # Get policy data
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Get file data
        file_path = policy["file_path"]
        file_data = await self.storage_service.get_file_content(file_path)
        
        # Extract text content based on file type
        mime_type = policy["mime_type"]
        text_content = await self._extract_text_content(file_data, mime_type)
        
        # Perform content checks
        issues = []
        
        # Check for minimum content length
        if len(text_content) < 100:
            issues.append("Policy content is too short (less than 100 characters)")
        
        # Check for required sections based on document type
        required_sections = await self._get_required_sections(policy["document_type"])
        missing_sections = []
        
        for section in required_sections:
            if section.lower() not in text_content.lower():
                missing_sections.append(section)
        
        if missing_sections:
            issues.append(f"Missing required sections: {', '.join(missing_sections)}")
        
        # Determine overall validity
        is_valid = len(issues) == 0
        
        # Compile verification details
        details = {
            "content_length": len(text_content),
            "issues": issues,
            "missing_sections": missing_sections
        }
        
        # Log verification result
        verification_log = {
            "policy_id": policy_id,
            "verification_type": "content",
            "is_valid": is_valid,
            "details": json.dumps(details),
            "verified_at": datetime.now(),
            "verified_by": "system"
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_verification_logs (
                policy_id, verification_type, is_valid, details, verified_at, verified_by
            ) VALUES (
                :policy_id, :verification_type, :is_valid, :details, :verified_at, :verified_by
            )
            """,
            verification_log
        )
        
        await self.db_session.commit()
        
        return {
            "policy_id": policy_id,
            "is_valid": is_valid,
            "verification_type": "content",
            "details": details
        }
    
    async def verify_all(self, policy_id: UUID) -> Dict:
        """Run all verification checks on a policy."""
        integrity_result = await self.verify_policy_integrity(policy_id)
        metadata_result = await self.verify_policy_metadata(policy_id)
        content_result = await self.verify_policy_content(policy_id)
        
        # Determine overall validity
        is_valid = (
            integrity_result["is_valid"] and
            metadata_result["is_valid"] and
            content_result["is_valid"]
        )
        
        return {
            "policy_id": policy_id,
            "is_valid": is_valid,
            "integrity": integrity_result,
            "metadata": metadata_result,
            "content": content_result
        }
    
    async def _verify_fema_reference(self, reference_id: str) -> Tuple[bool, Dict]:
        """Verify a FEMA reference ID against the FEMA API."""
        if not self.fema_api_url or not self.fema_api_key:
            return None, None
        
        try:
            response = requests.get(
                f"{self.fema_api_url}/policies/{reference_id}",
                headers={"Authorization": f"Bearer {self.fema_api_key}"}
            )
            
            if response.status_code == 200:
                data = response.json()
                return True, {
                    "status": "valid",
                    "fema_data": data
                }
            elif response.status_code == 404:
                return False, {
                    "status": "invalid",
                    "error": "FEMA reference ID not found"
                }
            else:
                return None, {
                    "status": "unknown",
                    "error": f"API error: {response.status_code}"
                }
        except Exception as e:
            return None, {
                "status": "error",
                "error": str(e)
            }
    
    async def _extract_text_content(self, file_data: bytes, mime_type: str) -> str:
        """Extract text content from a file based on its MIME type."""
        if mime_type == "application/pdf":
            # Use a PDF extraction library or service
            # This is a simplified example
            return "PDF content extraction placeholder"
        elif mime_type.startswith("text/"):
            # Plain text
            return file_data.decode("utf-8")
        elif mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            # Word document
            # Use a Word extraction library or service
            return "Word document content extraction placeholder"
        else:
            return "Unsupported file type for content extraction"
    
    async def _get_required_sections(self, document_type: str) -> List[str]:
        """Get required sections for a specific document type."""
        # This would typically come from a configuration or database
        if document_type == "POLICY_GUIDE":
            return ["Introduction", "Scope", "Policy Statement", "Procedures", "References"]
        elif document_type == "FACT_SHEET":
            return ["Overview", "Eligibility", "Requirements", "Contact Information"]
        elif document_type == "TECHNICAL_GUIDE":
            return ["Purpose", "Technical Requirements", "Implementation", "Compliance"]
        else:
            return []
```

### 4.2 Policy Verification Watcher (Python)

```python
#!/usr/bin/env python3
"""
Policy Verification Watcher

This script monitors policy documents for changes and automatically verifies
their integrity, metadata, and content. It also checks for updates to FEMA
policies and alerts when local policies may need updates.
"""

import asyncio
import logging
import os
import sys
import time
import json
import requests
import hashlib
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sqlite3
import feedparser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("policy_watcher.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("policy_watcher")

class PolicyWatcher:
    def __init__(self, config_path: str):
        """Initialize the policy watcher with configuration."""
        self.config = self._load_config(config_path)
        self.db_path = self.config.get("db_path", "policy_watcher.db")
        self.policies_dir = self.config.get("policies_dir", "policies")
        self.fema_rss_url = self.config.get("fema_rss_url")
        self.fema_api_url = self.config.get("fema_api_url")
        self.fema_api_key = self.config.get("fema_api_key")
        self.check_interval = self.config.get("check_interval", 3600)  # Default: 1 hour
        self.notification_url = self.config.get("notification_url")
        
        # Initialize database
        self._init_db()
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return {}
    
    def _init_db(self):
        """Initialize the SQLite database for tracking policy states."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS policy_states (
            policy_id TEXT PRIMARY KEY,
            file_path TEXT NOT NULL,
            last_modified TEXT NOT NULL,
            last_checksum TEXT NOT NULL,
            last_verified TEXT NOT NULL
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS fema_updates (
            update_id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            link TEXT NOT NULL,
            published_date TEXT NOT NULL,
            description TEXT,
            is_processed BOOLEAN DEFAULT 0
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS verification_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            policy_id TEXT NOT NULL,
            verification_type TEXT NOT NULL,
            is_valid BOOLEAN NOT NULL,
            details TEXT,
            verified_at TEXT NOT NULL,
            FOREIGN KEY (policy_id) REFERENCES policy_states (policy_id)
        )
        ''')
        
        conn.commit()
        conn.close()
    
    async def run(self):
        """Run the policy watcher continuously."""
        logger.info("Starting Policy Watcher")
        
        while True:
            try:
                # Check local policies
                await self.check_local_policies()
                
                # Check FEMA updates
                if self.fema_rss_url:
                    await self.check_fema_updates()
                
                # Wait for next check
                logger.info(f"Waiting {self.check_interval} seconds until next check")
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in watcher main loop: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    async def check_local_policies(self):
        """Check local policy files for changes and verify them."""
        logger.info("Checking local policies")
        
        # Get list of policy files
        policy_files = []
        for root, _, files in os.walk(self.policies_dir):
            for file in files:
                if file.endswith(('.json', '.pdf', '.docx', '.txt')):
                    policy_files.append(os.path.join(root, file))
        
        # Check each policy file
        for file_path in policy_files:
            try:
                await self.verify_policy_file(file_path)
            except Exception as e:
                logger.error(f"Error verifying policy file {file_path}: {e}")
    
    async def verify_policy_file(self, file_path: str):
        """Verify a single policy file."""
        # Get file metadata
        stat = os.stat(file_path)
        last_modified = datetime.fromtimestamp(stat.st_mtime).isoformat()
        
        # Calculate checksum
        with open(file_path, 'rb') as f:
            file_data = f.read()
            checksum = hashlib.sha256(file_data).hexdigest()
        
        # Get policy ID from filename or metadata
        policy_id = os.path.basename(file_path).split('.')[0]
        
        # Check if policy has changed
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT last_modified, last_checksum FROM policy_states WHERE policy_id = ?",
            (policy_id,)
        )
        result = cursor.fetchone()
        
        if not result or result[0] != last_modified or result[1] != checksum:
            # Policy is new or has changed
            logger.info(f"Policy {policy_id} has changed, verifying")
            
            # Perform verification
            verification_results = {
                "integrity": self._verify_integrity(file_path, checksum),
                "metadata": self._verify_metadata(file_path),
                "content": self._verify_content(file_path)
            }
            
            # Store verification results
            for v_type, result in verification_results.items():
                cursor.execute(
                    """
                    INSERT INTO verification_results 
                    (policy_id, verification_type, is_valid, details, verified_at)
                    VALUES (?, ?, ?, ?, ?)
                    """,
                    (
                        policy_id, 
                        v_type, 
                        result["is_valid"], 
                        json.dumps(result["details"]), 
                        datetime.now().isoformat()
                    )
                )
            
            # Update policy state
            cursor.execute(
                """
                INSERT OR REPLACE INTO policy_states 
                (policy_id, file_path, last_modified, last_checksum, last_verified)
                VALUES (?, ?, ?, ?, ?)
                """,
                (
                    policy_id,
                    file_path,
                    last_modified,
                    checksum,
                    datetime.now().isoformat()
                )
            )
            
            # Send notification if any verification failed
            if not all(r["is_valid"] for r in verification_results.values()):
                self._send_notification(
                    f"Policy verification failed for {policy_id}",
                    {
                        "policy_id": policy_id,
                        "file_path": file_path,
                        "verification_results": verification_results
                    }
                )
        
        conn.commit()
        conn.close()
    
    def _verify_integrity(self, file_path: str, checksum: str) -> Dict:
        """Verify file integrity."""
        # In a real implementation, this might compare against a stored checksum
        # or digital signature. For this example, we'll just check file exists.
        is_valid = os.path.exists(file_path) and os.path.getsize(file_path) > 0
        
        return {
            "is_valid": is_valid,
            "details": {
                "checksum": checksum,
                "file_size": os.path.getsize(file_path) if is_valid else 0
            }
        }
    
    def _verify_metadata(self, file_path: str) -> Dict:
        """Verify policy metadata."""
        # In a real implementation, this would parse the file and check metadata
        # For this example, we'll just check if it's a valid JSON file for JSON files
        is_valid = True
        details = {}
        
        if file_path.endswith('.json'):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                # Check required fields
                required_fields = ["title", "effectiveDate", "status"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    is_valid = False
                    details["missing_fields"] = missing_fields
            except json.JSONDecodeError:
                is_valid = False
                details["error"] = "Invalid JSON format"
        
        return {
            "is_valid": is_valid,
            "details": details
        }
    
    def _verify_content(self, file_path: str) -> Dict:
        """Verify policy content."""
        # In a real implementation, this would analyze content for compliance
        # For this example, we'll just check file size is reasonable
        file_size = os.path.getsize(file_path)
        is_valid = file_size > 100  # Arbitrary minimum size
        
        return {
            "is_valid": is_valid,
            "details": {
                "file_size": file_size
            }
        }
    
    async def check_fema_updates(self):
        """Check for updates to FEMA policies via RSS feed."""
        logger.info("Checking FEMA updates")
        
        try:
            feed = feedparser.parse(self.fema_rss_url)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for entry in feed.entries:
                # Extract entry data
                update_id = entry.id if hasattr(entry, 'id') else entry.link
                title = entry.title
                link = entry.link
                published_date = entry.published if hasattr(entry, 'published') else datetime.now().isoformat()
                description = entry.description if hasattr(entry, 'description') else ""
                
                # Check if we've seen this update before
                cursor.execute(
                    "SELECT update_id FROM fema_updates WHERE update_id = ?",
                    (update_id,)
                )
                result = cursor.fetchone()
                
                if not result:
                    # New update
                    logger.info(f"New FEMA update: {title}")
                    
                    cursor.execute(
                        """
                        INSERT INTO fema_updates 
                        (update_id, title, link, published_date, description)
                        VALUES (?, ?, ?, ?, ?)
                        """,
                        (update_id, title, link, published_date, description)
                    )
                    
                    # Send notification
                    self._send_notification(
                        f"New FEMA policy update: {title}",
                        {
                            "update_id": update_id,
                            "title": title,
                            "link": link,
                            "published_date": published_date,
                            "description": description
                        }
                    )
            
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"Error checking FEMA updates: {e}")
    
    def _send_notification(self, subject: str, data: Dict):
        """Send a notification about policy changes or verification issues."""
        if not self.notification_url:
            logger.info(f"Notification: {subject}")
            return
        
        try:
            response = requests.post(
                self.notification_url,
                json={
                    "subject": subject,
                    "data": data,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            if response.status_code != 200:
                logger.error(f"Error sending notification: {response.status_code} {response.text}")
        except Exception as e:
            logger.error(f"Error sending notification: {e}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Policy Verification Watcher")
    parser.add_argument(
        "--config", 
        default="config.json", 
        help="Path to configuration file"
    )
    args = parser.parse_args()
    
    watcher = PolicyWatcher(args.config)
    
    # Run the watcher
    asyncio.run(watcher.run())

if __name__ == "__main__":
    main()
```

### 4.3 Policy Verification API Endpoints

```python
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from typing import List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.policy_verification import (
    PolicyVerificationRequest,
    PolicyVerificationResponse,
    PolicyVerificationLogResponse
)
from app.services.policy_verification import PolicyVerificationService
from app.db.session import get_db
from app.auth.dependencies import get_current_user

router = APIRouter(prefix="/api/policy-verification", tags=["policy-verification"])

@router.post("/{policy_id}/verify", response_model=PolicyVerificationResponse)
async def verify_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to verify"),
    verification_types: List[str] = Query(
        ["integrity", "metadata", "content"], 
        description="Types of verification to perform"
    ),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Verify a policy document."""
    verification_service = PolicyVerificationService(db)
    
    results = {}
    
    if "all" in verification_types or "integrity" in verification_types:
        results["integrity"] = await verification_service.verify_policy_integrity(policy_id)
    
    if "all" in verification_types or "metadata" in verification_types:
        results["metadata"] = await verification_service.verify_policy_metadata(policy_id)
    
    if "all" in verification_types or "content" in verification_types:
        results["content"] = await verification_service.verify_policy_content(policy_id)
    
    # Determine overall validity
    is_valid = all(result["is_valid"] for result in results.values())
    
    return {
        "policy_id": policy_id,
        "is_valid": is_valid,
        "results": results
    }

@router.get("/{policy_id}/logs", response_model=List[PolicyVerificationLogResponse])
async def get_verification_logs(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    verification_type: Optional[str] = Query(
        None, 
        description="Filter by verification type"
    ),
    limit: int = Query(20, ge=1, le=100),
    skip: int = Query(0, ge=0),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get verification logs for a policy."""
    verification_service = PolicyVerificationService(db)
    
    return await verification_service.get_verification_logs(
        policy_id,
        verification_type=verification_type,
        limit=limit,
        skip=skip
    )

@router.post("/batch-verify", response_model=List[PolicyVerificationResponse])
async def batch_verify_policies(
    request: PolicyVerificationRequest,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Verify multiple policies at once."""
    verification_service = PolicyVerificationService(db)
    
    results = []
    
    for policy_id in request.policy_ids:
        try:
            result = await verification_service.verify_all(policy_id)
            results.append(result)
        except Exception as e:
            results.append({
                "policy_id": policy_id,
                "is_valid": False,
                "error": str(e)
            })
    
    return results

@router.get("/statistics", response_model=dict)
async def get_verification_statistics(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get statistics about policy verification."""
    verification_service = PolicyVerificationService(db)
    
    return await verification_service.get_verification_statistics()
```

### 4.4 Unit Tests for Policy Verification

```python
import unittest
from unittest.mock import AsyncMock, MagicMock, patch
import hashlib
import json
from datetime import datetime
from uuid import uuid4

from app.services.policy_verification import PolicyVerificationService

class TestPolicyVerificationService(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.db_session = AsyncMock()
        self.storage_service = AsyncMock()
        self.config = {
            "FEMA_API_URL": "https://api.fema.gov",
            "FEMA_API_KEY": "test_api_key"
        }
        
        self.service = PolicyVerificationService(
            self.db_session,
            self.storage_service,
            self.config
        )
        
        # Sample policy data
        self.policy_id = uuid4()
        self.policy_data = {
            "id": self.policy_id,
            "title": "Test Policy",
            "description": "Test policy description",
            "document_type": "POLICY_GUIDE",
            "file_path": "/path/to/test_policy.pdf",
            "original_filename": "test_policy.pdf",
            "mime_type": "application/pdf",
            "file_size": 1024,
            "status": "PUBLISHED",
            "effective_date": datetime(2025, 1, 1),
            "expiration_date": datetime(2026, 1, 1),
            "version": 1,
            "checksum": "sample_checksum",
            "fema_reference_id": "FEMA-2025-01",
            "created_at": datetime(2024, 12, 1),
            "updated_at": datetime(2024, 12, 1)
        }
        
        # Mock database query result
        self.db_session.execute.return_value.fetchone.return_value = self.policy_data
    
    @patch('hashlib.sha256')
    async def test_verify_policy_integrity_valid(self, mock_sha256):
        """Test policy integrity verification with valid checksum."""
        # Mock file content and checksum
        file_content = b"test file content"
        self.storage_service.get_file_content.return_value = file_content
        
        # Mock hashlib to return the same checksum
        mock_hash = MagicMock()
        mock_hash.hexdigest.return_value = "sample_checksum"
        mock_sha256.return_value = mock_hash
        
        # Call the method
        result = await self.service.verify_policy_integrity(self.policy_id)
        
        # Assertions
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "integrity")
        self.assertEqual(result["details"]["stored_checksum"], "sample_checksum")
        self.assertEqual(result["details"]["calculated_checksum"], "sample_checksum")
        
        # Verify method calls
        self.db_session.execute.assert_called_once()
        self.storage_service.get_file_content.assert_called_once_with(self.policy_data["file_path"])
        mock_sha256.assert_called_once_with(file_content)
        self.db_session.commit.assert_called_once()
    
    @patch('hashlib.sha256')
    async def test_verify_policy_integrity_invalid(self, mock_sha256):
        """Test policy integrity verification with invalid checksum."""
        # Mock file content and checksum
        file_content = b"test file content"
        self.storage_service.get_file_content.return_value = file_content
        
        # Mock hashlib to return a different checksum
        mock_hash = MagicMock()
        mock_hash.hexdigest.return_value = "different_checksum"
        mock_sha256.return_value = mock_hash
        
        # Call the method
        result = await self.service.verify_policy_integrity(self.policy_id)
        
        # Assertions
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "integrity")
        self.assertEqual(result["details"]["stored_checksum"], "sample_checksum")
        self.assertEqual(result["details"]["calculated_checksum"], "different_checksum")
    
    async def test_verify_policy_metadata_valid(self):
        """Test policy metadata verification with valid metadata."""
        # Call the method
        result = await self.service.verify_policy_metadata(self.policy_id)
        
        # Assertions
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "metadata")
        self.assertEqual(result["details"]["missing_fields"], [])
        self.assertEqual(result["details"]["date_issues"], [])
    
    async def test_verify_policy_metadata_invalid_dates(self):
        """Test policy metadata verification with invalid dates."""
        # Modify policy data to have invalid dates
        modified_policy = dict(self.policy_data)
        modified_policy["effective_date"] = datetime(2026, 1, 1)
        modified_policy["expiration_date"] = datetime(2025, 1, 1)
        
        self.db_session.execute.return_value.fetchone.return_value = modified_policy
        
        # Call the method
        result = await self.service.verify_policy_metadata(self.policy_id)
        
        # Assertions
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "metadata")
        self.assertEqual(result["details"]["missing_fields"], [])
        self.assertIn("Expiration date must be after effective date", result["details"]["date_issues"])
    
    async def test_verify_policy_metadata_missing_fields(self):
        """Test policy metadata verification with missing required fields."""
        # Modify policy data to have missing fields
        modified_policy = dict(self.policy_data)
        modified_policy["title"] = None
        
        self.db_session.execute.return_value.fetchone.return_value = modified_policy
        
        # Call the method
        result = await self.service.verify_policy_metadata(self.policy_id)
        
        # Assertions
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "metadata")
        self.assertIn("title", result["details"]["missing_fields"])
    
    @patch('app.services.policy_verification.PolicyVerificationService._extract_text_content')
    async def test_verify_policy_content_valid(self, mock_extract_text):
        """Test policy content verification with valid content."""
        # Mock text extraction
        mock_extract_text.return_value = """
        Introduction
        This is a test policy document with all required sections.
        
        Scope
        This policy applies to all employees.
        
        Policy Statement
        This is the policy statement.
        
        Procedures
        These are the procedures to follow.
        
        References
        These are the references.
        """
        
        # Call the method
        result = await self.service.verify_policy_content(self.policy_id)
        
        # Assertions
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "content")
        self.assertEqual(result["details"]["missing_sections"], [])
    
    @patch('app.services.policy_verification.PolicyVerificationService._extract_text_content')
    async def test_verify_policy_content_missing_sections(self, mock_extract_text):
        """Test policy content verification with missing required sections."""
        # Mock text extraction with missing sections
        mock_extract_text.return_value = """
        Introduction
        This is a test policy document with missing required sections.
        
        Scope
        This policy applies to all employees.
        """
        
        # Call the method
        result = await self.service.verify_policy_content(self.policy_id)
        
        # Assertions
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertEqual(result["verification_type"], "content")
        self.assertTrue(len(result["details"]["missing_sections"]) > 0)
    
    async def test_verify_all(self):
        """Test running all verification checks."""
        # Mock individual verification methods
        self.service.verify_policy_integrity = AsyncMock(return_value={
            "policy_id": self.policy_id,
            "is_valid": True,
            "verification_type": "integrity",
            "details": {}
        })
        
        self.service.verify_policy_metadata = AsyncMock(return_value={
            "policy_id": self.policy_id,
            "is_valid": True,
            "verification_type": "metadata",
            "details": {}
        })
        
        self.service.verify_policy_content = AsyncMock(return_value={
            "policy_id": self.policy_id,
            "is_valid": True,
            "verification_type": "content",
            "details": {}
        })
        
        # Call the method
        result = await self.service.verify_all(self.policy_id)
        
        # Assertions
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["policy_id"], self.policy_id)
        self.assertIn("integrity", result)
        self.assertIn("metadata", result)
        self.assertIn("content", result)
        
        # Verify method calls
        self.service.verify_policy_integrity.assert_called_once_with(self.policy_id)
        self.service.verify_policy_metadata.assert_called_once_with(self.policy_id)
        self.service.verify_policy_content.assert_called_once_with(self.policy_id)

if __name__ == '__main__':
    unittest.main()
```

## 5. Policy Information Lifecycle Management

### 5.1 Policy Lifecycle Management Approach

```mermaid
stateDiagram-v2
    [*] --> Creation: New policy need identified
    Creation --> Draft: Initial draft created
    Draft --> Review: Submit for review
    Review --> Revision: Feedback provided
    Revision --> Review: Resubmit
    Review --> Approval: Review passed
    Approval --> Publication: Approved
    Publication --> Active: Published
    Active --> Update: Policy change needed
    Update --> Draft: New version created
    Active --> Superseded: Replaced by new policy
    Active --> Expiration: Expiration date reached
    Superseded --> Archival: Archive superseded policy
    Expiration --> Archival: Archive expired policy
    Archival --> Retention: Begin retention period
    Retention --> Deletion: Retention period ended
    Deletion --> [*]
```

### 5.2 Policy Lifecycle Management Service

```python
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from uuid import UUID
import json
import os

class PolicyLifecycleService:
    def __init__(self, db_session, storage_service, notification_service, config):
        self.db_session = db_session
        self.storage_service = storage_service
        self.notification_service = notification_service
        self.config = config
        
        # Load retention configuration
        self.retention_config = self.config.get("retention", {
            "default": 7 * 365,  # 7 years in days
            "by_type": {
                "FACT_SHEET": 2 * 365,  # 2 years
                "TECHNICAL_GUIDE": 5 * 365,  # 5 years
                "POLICY_GUIDE": 10 * 365,  # 10 years
            }
        })
    
    async def check_policy_lifecycle_status(self):
        """Check all policies for lifecycle status changes."""
        today = datetime.now().date()
        
        # Check for policies that have reached their expiration date
        expired_policies = await self.db_session.execute(
            """
            SELECT id, title, document_type, effective_date, expiration_date
            FROM policy_documents
            WHERE status = 'PUBLISHED'
            AND expiration_date <= :today
            """,
            {"today": today}
        )
        
        for policy in expired_policies:
            await self.expire_policy(policy["id"])
        
        # Check for policies that need to be archived based on retention policy
        archived_policies = await self.db_session.execute(
            """
            SELECT id, title, document_type, status, updated_at
            FROM policy_documents
            WHERE status IN ('SUPERSEDED', 'ARCHIVED')
            AND archive_date IS NOT NULL
            """,
        )
        
        for policy in archived_policies:
            retention_days = self._get_retention_period(policy["document_type"])
            archive_date = policy["archive_date"].date()
            retention_end_date = archive_date + timedelta(days=retention_days)
            
            if today >= retention_end_date:
                await self.delete_policy(policy["id"])
    
    async def expire_policy(self, policy_id: UUID) -> Dict:
        """Mark a policy as expired and archive it."""
        # Get policy data
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Update policy status
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET status = 'ARCHIVED', 
                archive_date = :archive_date,
                last_updated_by = 'system',
                updated_at = NOW()
            WHERE id = :id
            """,
            {"id": policy_id, "archive_date": datetime.now()}
        )
        
        # Create change log entry
        change_log_data = {
            "policy_id": policy_id,
            "version_id": policy["version_id"],
            "change_type": "EDITORIAL",
            "description": "Policy expired and archived automatically",
            "changed_by": "system"
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_change_logs (
                policy_id, version_id, change_type, description, changed_by
            ) VALUES (
                :policy_id, :version_id, :change_type, :description, :changed_by
            )
            """,
            change_log_data
        )
        
        await self.db_session.commit()
        
        # Notify stakeholders
        await self.notification_service.notify_policy_expired(policy_id)
        
        return {
            "policy_id": policy_id,
            "status": "ARCHIVED",
            "archive_date": datetime.now().isoformat()
        }
    
    async def archive_policy(self, policy_id: UUID, reason: str, archived_by: Optional[UUID] = None) -> Dict:
        """Archive a policy."""
        # Get policy data
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Update policy status
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET status = 'ARCHIVED', 
                archive_date = :archive_date,
                last_updated_by = :last_updated_by,
                updated_at = NOW()
            WHERE id = :id
            """,
            {
                "id": policy_id, 
                "archive_date": datetime.now(),
                "last_updated_by": archived_by or "system"
            }
        )
        
        # Create change log entry
        change_log_data = {
            "policy_id": policy_id,
            "version_id": policy["version_id"],
            "change_type": "EDITORIAL",
            "description": f"Policy archived: {reason}",
            "changed_by": archived_by or "system"
        }
        
        await self.db_session.execute(
            """
            INSERT INTO policy_change_logs (
                policy_id, version_id, change_type, description, changed_by
            ) VALUES (
                :policy_id, :version_id, :change_type, :description, :changed_by
            )
            """,
            change_log_data
        )
        
        await self.db_session.commit()
        
        # Notify stakeholders
        await self.notification_service.notify_policy_archived(policy_id, reason)
        
        return {
            "policy_id": policy_id,
            "status": "ARCHIVED",
            "archive_date": datetime.now().isoformat(),
            "reason": reason
        }
    
    async def delete_policy(self, policy_id: UUID) -> Dict:
        """Delete a policy after retention period."""
        # Get policy data
        policy = await self.db_session.execute(
            "SELECT * FROM policy_documents WHERE id = :id",
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Archive the file first
        file_path = policy["file_path"]
        archive_path = await self.storage_service.archive_file(file_path)
        
        # Update policy record to mark as deleted
        await self.db_session.execute(
            """
            UPDATE policy_documents 
            SET is_deleted = TRUE, 
                deleted_at = NOW(),
                archive_path = :archive_path
            WHERE id = :id
            """,
            {"id": policy_id, "archive_path": archive_path}
        )
        
        # Log the deletion
        await self.db_session.execute(
            """
            INSERT INTO policy_deletion_logs (
                policy_id, policy_title, document_type, version, 
                original_file_path, archive_path, deleted_at
            ) VALUES (
                :policy_id, :policy_title, :document_type, :version,
                :original_file_path, :archive_path, NOW()
            )
            """,
            {
                "policy_id": policy_id,
                "policy_title": policy["title"],
                "document_type": policy["document_type"],
                "version": policy["version"],
                "original_file_path": file_path,
                "archive_path": archive_path
            }
        )
        
        await self.db_session.commit()
        
        # Notify administrators
        await self.notification_service.notify_policy_deleted(policy_id)
        
        return {
            "policy_id": policy_id,
            "status": "DELETED",
            "deleted_at": datetime.now().isoformat(),
            "archive_path": archive_path
        }
    
    async def get_lifecycle_status(self, policy_id: UUID) -> Dict:
        """Get the lifecycle status of a policy."""
        # Get policy data
        policy = await self.db_session.execute(
            """
            SELECT id, title, document_type, status, effective_date, 
                   expiration_date, version, created_at, updated_at,
                   archive_date, is_deleted, deleted_at
            FROM policy_documents 
            WHERE id = :id
            """,
            {"id": policy_id}
        )
        policy = policy.fetchone()
        
        if not policy:
            raise ValueError(f"Policy with ID {policy_id} not found")
        
        # Determine lifecycle stage
        lifecycle_stage = self._determine_lifecycle_stage(policy)
        
        # Calculate retention information if applicable
        retention_info = None
        if policy["status"] in ["ARCHIVED", "SUPERSEDED"] and policy["archive_date"]:
            retention_days = self._get_retention_period(policy["document_type"])
            retention_end_date = policy["archive_date"] + timedelta(days=retention_days)
            days_remaining = (retention_end_date - datetime.now()).days
            
            retention_info = {
                "retention_period_days": retention_days,
                "retention_end_date": retention_end_date.isoformat(),
                "days_remaining": max(0, days_remaining)
            }
        
        return {
            "policy_id": policy_id,
            "title": policy["title"],
            "status": policy["status"],
            "lifecycle_stage": lifecycle_stage,
            "effective_date": policy["effective_date"].isoformat() if policy["effective_date"] else None,
            "expiration_date": policy["expiration_date"].isoformat() if policy["expiration_date"] else None,
            "version": policy["version"],
            "created_at": policy["created_at"].isoformat(),
            "updated_at": policy["updated_at"].isoformat(),
            "archive_date": policy["archive_date"].isoformat() if policy["archive_date"] else None,
            "is_deleted": policy["is_deleted"],
            "deleted_at": policy["deleted_at"].isoformat() if policy["deleted_at"] else None,
            "retention_info": retention_info
        }
    
    def _determine_lifecycle_stage(self, policy: Dict) -> str:
        """Determine the lifecycle stage of a policy based on its status and dates."""
        if policy["is_deleted"]:
            return "DELETED"
        
        status = policy["status"]
        now = datetime.now()
        
        if status == "DRAFT":
            return "DRAFT"
        elif status == "REVIEW":
            return "REVIEW"
        elif status == "APPROVED":
            return "APPROVED"
        elif status == "PUBLISHED":
            if policy["effective_date"] > now:
                return "PENDING_EFFECTIVE"
            elif policy["expiration_date"] and policy["expiration_date"] 
< now:
                return "ACTIVE"
            else:
                return "ACTIVE"
        elif status == "ARCHIVED":
            return "ARCHIVED"
        elif status == "SUPERSEDED":
            return "SUPERSEDED"
        else:
            return "UNKNOWN"
    
    def _get_retention_period(self, document_type: str) -> int:
        """Get the retention period in days for a document type."""
        by_type = self.retention_config.get("by_type", {})
        return by_type.get(document_type, self.retention_config.get("default", 7 * 365))
```

### 5.3 Policy Retention Configuration

```json
{
  "retention": {
    "default": 2555,  // 7 years in days
    "by_type": {
      "POLICY_GUIDE": 3650,  // 10 years
      "TECHNICAL_GUIDE": 1825,  // 5 years
      "FACT_SHEET": 730,  // 2 years
      "FORM": 1095,  // 3 years
      "TEMPLATE": 1095,  // 3 years
      "PROCEDURE": 1825,  // 5 years
      "STANDARD": 3650,  // 10 years
      "REGULATION": 3650,  // 10 years
      "GUIDANCE": 1825,  // 5 years
      "MEMO": 730,  // 2 years
      "REPORT": 1095,  // 3 years
      "OTHER": 1095  // 3 years
    },
    "by_status": {
      "DRAFT": 365,  // 1 year for drafts
      "SUPERSEDED": 1825,  // 5 years for superseded policies
      "ARCHIVED": 2555  // 7 years for archived policies
    },
    "legal_hold": {
      "enabled": true,
      "override_retention": true,
      "notification_emails": ["<EMAIL>", "<EMAIL>"]
    }
  }
}
```

### 5.4 Policy Lifecycle API Endpoints

```python
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from typing import List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.policy_lifecycle import (
    PolicyLifecycleStatusResponse,
    PolicyArchiveRequest,
    PolicyLifecycleActionResponse
)
from app.services.policy_lifecycle import PolicyLifecycleService
from app.db.session import get_db
from app.auth.dependencies import get_current_user, require_permission

router = APIRouter(prefix="/api/policy-lifecycle", tags=["policy-lifecycle"])

@router.get("/{policy_id}/status", response_model=PolicyLifecycleStatusResponse)
async def get_policy_lifecycle_status(
    policy_id: UUID = Path(..., description="The ID of the policy"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get the lifecycle status of a policy."""
    lifecycle_service = PolicyLifecycleService(db)
    
    try:
        return await lifecycle_service.get_lifecycle_status(policy_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/{policy_id}/archive", response_model=PolicyLifecycleActionResponse)
async def archive_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to archive"),
    request: PolicyArchiveRequest = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(require_permission("policy:archive"))
):
    """Archive a policy."""
    lifecycle_service = PolicyLifecycleService(db)
    
    try:
        return await lifecycle_service.archive_policy(
            policy_id, 
            request.reason, 
            current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/{policy_id}/expire", response_model=PolicyLifecycleActionResponse)
async def expire_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to expire"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(require_permission("policy:manage_lifecycle"))
):
    """Manually expire a policy."""
    lifecycle_service = PolicyLifecycleService(db)
    
    try:
        return await lifecycle_service.expire_policy(policy_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/{policy_id}/delete", response_model=PolicyLifecycleActionResponse)
async def delete_policy(
    policy_id: UUID = Path(..., description="The ID of the policy to delete"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(require_permission("policy:delete"))
):
    """Delete a policy (admin only)."""
    lifecycle_service = PolicyLifecycleService(db)
    
    try:
        return await lifecycle_service.delete_policy(policy_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/check-lifecycle-status", response_model=dict)
async def check_all_policies_lifecycle_status(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(require_permission("policy:manage_lifecycle"))
):
    """Check all policies for lifecycle status changes."""
    lifecycle_service = PolicyLifecycleService(db)
    
    await lifecycle_service.check_policy_lifecycle_status()
    
    return {"status": "success", "message": "Lifecycle status check completed"}
```

### 5.5 Policy Lifecycle Management Dashboard Component (React)

```jsx
// components/PolicyLifecycleDashboard.jsx
import React, { useState, useEffect } from 'react';
import { 
  Box, Typography, Paper, Grid, Card, CardContent, 
  CardActions, Button, Chip, CircularProgress, 
  Table, TableBody, TableCell, TableContainer, 
  TableHead, TableRow, Alert, Divider
} from '@mui/material';
import {
  PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, 
  CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { 
  Archive as ArchiveIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { usePolicyLifecycle } from '../hooks/usePolicyLifecycle';
import ConfirmationDialog from './ConfirmationDialog';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#FF6B6B'];

const PolicyLifecycleDashboard = () => {
  const navigate = useNavigate();
  const { 
    loading, 
    error, 
    statistics, 
    expiringPolicies,
    retentionPolicies,
    fetchLifecycleData,
    checkLifecycleStatus
  } = usePolicyLifecycle();
  
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    action: null
  });
  
  useEffect(() => {
    fetchLifecycleData();
  }, []);
  
  const handleRefresh = () => {
    fetchLifecycleData();
  };
  
  const handleCheckLifecycleStatus = () => {
    setConfirmDialog({
      open: true,
      title: 'Check Lifecycle Status',
      message: 'This will check all policies for lifecycle status changes, including expiration and retention periods. Continue?',
      action: () => {
        checkLifecycleStatus().then(() => {
          fetchLifecycleData();
        });
      }
    });
  };
  
  const handleViewPolicy = (policyId) => {
    navigate(`/policies/${policyId}`);
  };
  
  const closeConfirmDialog = () => {
    setConfirmDialog({
      ...confirmDialog,
      open: false
    });
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading lifecycle data: {error}
      </Alert>
    );
  }
  
  // Prepare chart data
  const statusData = statistics?.statusCounts ? Object.keys(statistics.statusCounts).map(key => ({
    name: key,
    value: statistics.statusCounts[key]
  })) : [];
  
  const lifecycleStageData = statistics?.lifecycleStageCounts ? Object.keys(statistics.lifecycleStageCounts).map(key => ({
    name: key,
    value: statistics.lifecycleStageCounts[key]
  })) : [];
  
  const documentTypeData = statistics?.documentTypeCounts ? Object.keys(statistics.documentTypeCounts).map(key => ({
    name: key,
    value: statistics.documentTypeCounts[key]
  })) : [];
  
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Policy Lifecycle Management</Typography>
        
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={handleCheckLifecycleStatus}
          >
            Check Lifecycle Status
          </Button>
        </Box>
      </Box>
      
      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Total Policies</Typography>
              <Typography variant="h3">{statistics?.totalPolicies || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Active Policies</Typography>
              <Typography variant="h3">{statistics?.statusCounts?.PUBLISHED || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Expiring Soon</Typography>
              <Typography variant="h3">{expiringPolicies?.length || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>In Retention</Typography>
              <Typography variant="h3">{retentionPolicies?.length || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Charts */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>Policy Status Distribution</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>Lifecycle Stage Distribution</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={lifecycleStageData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        {/* Expiring Policies */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Policies Expiring Soon
              {expiringPolicies?.length > 0 && (
                <Chip 
                  icon={<WarningIcon />} 
                  label={`${expiringPolicies.length} policies`} 
                  color="warning" 
                  size="small"
                  sx={{ ml: 2 }}
                />
              )}
            </Typography>
            
            {expiringPolicies?.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Document Type</TableCell>
                      <TableCell>Effective Date</TableCell>
                      <TableCell>Expiration Date</TableCell>
                      <TableCell>Days Remaining</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {expiringPolicies.map(policy => (
                      <TableRow key={policy.id}>
                        <TableCell>{policy.title}</TableCell>
                        <TableCell>{policy.documentType}</TableCell>
                        <TableCell>{new Date(policy.effectiveDate).toLocaleDateString()}</TableCell>
                        <TableCell>{new Date(policy.expirationDate).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Chip 
                            label={policy.daysRemaining} 
                            color={
                              policy.daysRemaining <= 30 ? "error" :
                              policy.daysRemaining <= 90 ? "warning" : "success"
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button 
                            size="small" 
                            onClick={() => handleViewPolicy(policy.id)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" sx={{ mt: 2 }}>
                No policies expiring soon.
              </Typography>
            )}
          </Paper>
        </Grid>
        
        {/* Retention Policies */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Policies in Retention Period
            </Typography>
            
            {retentionPolicies?.length > 0 ? (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Document Type</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Archive Date</TableCell>
                      <TableCell>Retention End Date</TableCell>
                      <TableCell>Days Remaining</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {retentionPolicies.map(policy => (
                      <TableRow key={policy.id}>
                        <TableCell>{policy.title}</TableCell>
                        <TableCell>{policy.documentType}</TableCell>
                        <TableCell>
                          <Chip 
                            label={policy.status} 
                            color={
                              policy.status === 'ARCHIVED' ? "secondary" : "default"
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{new Date(policy.archiveDate).toLocaleDateString()}</TableCell>
                        <TableCell>{new Date(policy.retentionEndDate).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Chip 
                            label={policy.daysRemaining} 
                            color={
                              policy.daysRemaining <= 30 ? "error" :
                              policy.daysRemaining <= 90 ? "warning" : "success"
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button 
                            size="small" 
                            onClick={() => handleViewPolicy(policy.id)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" sx={{ mt: 2 }}>
                No policies in retention period.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
      
      <ConfirmationDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={() => {
          confirmDialog.action();
          closeConfirmDialog();
        }}
        onCancel={closeConfirmDialog}
      />
    </Box>
  );
};

export default PolicyLifecycleDashboard;
```

## 6. Implementation and Deployment Plan

### 6.1 Implementation Phases

| Phase | Description | Timeline | Dependencies |
|-------|-------------|----------|--------------|
| 1 | Database Schema Setup | Week 1 | None |
| 2 | Core Services Implementation | Weeks 2-3 | Phase 1 |
| 3 | API Endpoints Development | Weeks 3-4 | Phase 2 |
| 4 | Frontend Components Development | Weeks 4-6 | Phase 3 |
| 5 | Verification Mechanisms Implementation | Weeks 5-6 | Phase 2 |
| 6 | Lifecycle Management Implementation | Weeks 6-7 | Phase 2 |
| 7 | Integration Testing | Week 8 | Phases 1-6 |
| 8 | User Acceptance Testing | Week 9 | Phase 7 |
| 9 | Deployment and Training | Week 10 | Phase 8 |

### 6.2 Migration Strategy

1. **Database Migration**
   - Create new tables for policy management
   - Run migration scripts to populate initial data
   - Validate data integrity after migration

2. **Document Migration**
   - Inventory existing policy documents in Google Drive
   - Extract metadata from existing documents
   - Import documents into the new structured system
   - Validate document checksums and metadata

3. **User Training**
   - Develop training materials for policy administrators
   - Conduct training sessions for different user roles
   - Provide documentation and user guides

### 6.3 Deployment Checklist

- [ ] Database schema deployed and validated
- [ ] Backend services deployed and tested
- [ ] API endpoints accessible and secured
- [ ] Frontend components deployed and tested
- [ ] Verification mechanisms operational
- [ ] Lifecycle management processes tested
- [ ] User accounts and permissions configured
- [ ] Monitoring and alerting set up
- [ ] Backup and recovery procedures tested
- [ ] Documentation completed and distributed

## 7. Conclusion

The implementation of best practices for policy information maintenance in the ComplianceMax system will significantly enhance the organization's ability to manage FEMA Public Assistance program compliance. The structured approach to policy management, with robust versioning, metadata, and lifecycle management, ensures that policies remain current, accessible, and compliant with regulatory requirements.

Key benefits of this implementation include:

1. **Improved Policy Integrity**: The structured policy management system with versioning and metadata ensures that policies are properly documented, tracked, and maintained.

2. **Enhanced Compliance**: Verification mechanisms ensure that policies adhere to FEMA requirements and internal standards, reducing compliance risks.

3. **Streamlined Updates**: The enhanced update processes provide a clear workflow for policy changes, approvals, and publication, ensuring that updates are properly reviewed and documented.

4. **Better Accessibility**: The improved search and filtering capabilities make it easier for users to find relevant policies, increasing efficiency and compliance.

5. **Proper Lifecycle Management**: The comprehensive lifecycle approach ensures that policies are properly maintained from creation to deletion, with appropriate retention periods and archival procedures.

By implementing these best practices, ComplianceMax will establish a robust foundation for policy information maintenance that supports the organization's compliance objectives and enhances its ability to navigate the complex landscape of FEMA Public Assistance programs.
